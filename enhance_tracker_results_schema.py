#!/usr/bin/env python3
"""
Phase 1: Enhance Tracker Results Schema
Add department and section columns to tracker_results table
"""

import sys
import os
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def enhance_tracker_results_schema():
    """Add department and section columns to tracker_results table"""
    print("🔧 PHASE 1: ENHANCING TRACKER RESULTS SCHEMA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"📁 Using database: {db_path}")
        
        # 1. Check current tracker_results schema
        print("\n1. 📋 CURRENT TRACKER_RESULTS SCHEMA:")
        cursor.execute("PRAGMA table_info(tracker_results)")
        current_columns = cursor.fetchall()
        
        existing_columns = [col[1] for col in current_columns]
        print("   Current columns:")
        for col in current_columns:
            print(f"     {col[1]} {col[2]}")
        
        # 2. Add department column if it doesn't exist
        if 'department' not in existing_columns:
            print("\n2. ➕ ADDING DEPARTMENT COLUMN:")
            cursor.execute("ALTER TABLE tracker_results ADD COLUMN department TEXT")
            print("   ✅ Department column added successfully")
        else:
            print("\n2. ✅ DEPARTMENT COLUMN ALREADY EXISTS")
        
        # 3. Add section column if it doesn't exist
        if 'section' not in existing_columns:
            print("\n3. ➕ ADDING SECTION COLUMN:")
            cursor.execute("ALTER TABLE tracker_results ADD COLUMN section TEXT")
            print("   ✅ Section column added successfully")
        else:
            print("\n3. ✅ SECTION COLUMN ALREADY EXISTS")
        
        # 4. Verify the enhanced schema
        print("\n4. 📋 ENHANCED TRACKER_RESULTS SCHEMA:")
        cursor.execute("PRAGMA table_info(tracker_results)")
        enhanced_columns = cursor.fetchall()
        
        print("   Enhanced columns:")
        for col in enhanced_columns:
            print(f"     {col[1]} {col[2]}")
        
        # 5. Create index for better performance
        print("\n5. 🚀 CREATING PERFORMANCE INDEXES:")
        try:
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_tracker_results_department 
                ON tracker_results(department)
            """)
            print("   ✅ Department index created")
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_tracker_results_employee_dept 
                ON tracker_results(employee_id, department)
            """)
            print("   ✅ Employee-Department composite index created")
            
        except Exception as e:
            print(f"   ⚠️ Index creation warning: {e}")
        
        # 6. Get current data counts
        print("\n6. 📊 CURRENT DATA COUNTS:")
        cursor.execute("SELECT COUNT(*) FROM tracker_results")
        total_count = cursor.fetchone()[0]
        print(f"   Total tracker_results: {total_count}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NOT NULL")
        dept_count = cursor.fetchone()[0]
        print(f"   Records with department: {dept_count}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NULL")
        null_dept_count = cursor.fetchone()[0]
        print(f"   Records without department: {null_dept_count}")
        
        conn.commit()
        conn.close()
        
        print("\n✅ PHASE 1 COMPLETED SUCCESSFULLY")
        print("   - Department column added to tracker_results")
        print("   - Section column added to tracker_results")
        print("   - Performance indexes created")
        print(f"   - Ready for Phase 2: {null_dept_count} records need department data")
        
        return True
        
    except Exception as e:
        print(f"❌ Error enhancing schema: {e}")
        return False

if __name__ == "__main__":
    success = enhance_tracker_results_schema()
    sys.exit(0 if success else 1)
