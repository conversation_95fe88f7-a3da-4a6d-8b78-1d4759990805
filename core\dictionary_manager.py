#!/usr/bin/env python3
"""
THE PAYROLL AUDITOR - DICTIONARY MANAGER (STANDARDIZATION FOCUSED)
Manages label standardization for extracted payslip items.
NO CONSULTATIVE EXTRACTION - Pure standardization and reporting control.
"""

import json
import os
from typing import Dict, List, Optional, Any

class PayrollDictionaryManager:
    """
    STANDARDIZATION-FOCUSED Dictionary Manager for The Payroll Auditor.

    PURPOSE: Label standardization and reporting control ONLY.
    NO CONSULTATIVE EXTRACTION - Extractor works independently.

    Functions:
    - Standardize extracted label names
    - Control which items appear in reports
    - Manage label variations and aliases
    - Auto-learning for new extracted items
    """

    def __init__(self, dictionary_path: str = None, debug: bool = False, use_database: bool = True):
        self.debug = debug
        self.use_database = True  # Force database-only mode
        self.database = None

        if use_database:
            try:
                import sys
                import os

                # Add multiple possible paths to ensure module can be found
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(current_dir)  # Go up one level from core/
                parent_dir = os.path.dirname(project_root)   # Go up another level if needed

                # Add all possible paths
                for path in [project_root, parent_dir, current_dir]:
                    if path not in sys.path:
                        sys.path.insert(0, path)

                # Try different import patterns with proper path setup
                try:
                    from core.python_database_manager import get_database_instance
                except ImportError:
                    try:
                        # Add current directory to path and try direct import
                        sys.path.insert(0, current_dir)
                        from python_database_manager import get_database_instance
                    except ImportError:
                        try:
                            # Try importing from parent directory
                            sys.path.insert(0, project_root)
                            from core.python_database_manager import get_database_instance
                        except ImportError:
                            # Last resort: try with absolute path
                            import importlib.util
                            spec = importlib.util.spec_from_file_location(
                                "python_database_manager",
                                os.path.join(current_dir, "python_database_manager.py")
                            )
                            if spec and spec.loader:
                                module = importlib.util.module_from_spec(spec)
                                spec.loader.exec_module(module)
                                get_database_instance = module.get_database_instance
                            else:
                                raise ImportError("Could not load python_database_manager module")

                self.database = get_database_instance()
                if self.debug:
                    print("[DICTIONARY] Using Python SQLite database")
            except Exception as e:
                if self.debug:
                    import sys
                    print(f"[DICTIONARY] Database initialization failed: {e}", file=sys.stderr)
                    import traceback
                    traceback.print_exc(file=sys.stderr)
                raise Exception("Database connection required - JSON fallback disabled")

        if not self.use_database:
            # Fallback to JSON file storage
            if dictionary_path is None:
                base_dir = os.path.dirname(os.path.dirname(__file__))
                dictionary_path = os.path.join(base_dir, 'data', 'dictionaries', 'payslip_dictionary.json')
            self.dictionary_path = dictionary_path

        self.dictionary = {}

        # Load dictionary on initialization
        self.load_dictionary()

    def load_dictionary(self) -> bool:
        """
        Load the payslip dictionary from database or file.

        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if self.use_database and self.database:
                # Load from SQLite database
                return self._load_from_database()
            else:
                # Load from JSON file (fallback)
                return self._load_from_file()

        except Exception as e:
            if self.debug:
                print(f"[ERROR] Error loading dictionary: {e}")
            self._create_default_dictionary()
            return False

    def _load_from_database(self) -> bool:
        """Load dictionary from SQLite database using JSON storage"""
        try:
            import json

            # Try to load dictionary from database
            result = self.database.execute_query(
                'SELECT dictionary_data FROM payroll_dictionary ORDER BY updated_at DESC LIMIT 1'
            )

            if result and len(result) > 0:
                # Extract JSON data from result
                dictionary_json = result[0][0] if isinstance(result[0], (list, tuple)) else result[0]['dictionary_data']
                self.dictionary = json.loads(dictionary_json)

                if self.debug:
                    sections = len(self.dictionary)
                    total_items = sum(len(section.get('items', {})) for section in self.dictionary.values() if isinstance(section, dict))
                    print(f"[+] Dictionary loaded from database: {sections} sections, {total_items} items")

                return True
            else:
                # No data in database, create default
                if self.debug:
                    print("[INFO] No dictionary found in database, creating default")
                self._create_default_dictionary()
                return True

        except Exception as e:
            if self.debug:
                print(f"[ERROR] Database load failed: {e}")
            # Try to create default dictionary instead of failing
            self._create_default_dictionary()
            return True

    def _load_from_file(self) -> bool:
        """Load dictionary from JSON file (fallback)"""
        try:
            if os.path.exists(self.dictionary_path):
                with open(self.dictionary_path, 'r', encoding='utf-8') as f:
                    self.dictionary = json.load(f)

                if self.debug:
                    sections = len(self.dictionary)
                    total_items = sum(len(section.get('items', {})) for section in self.dictionary.values())
                    print(f"[+] Dictionary loaded from file: {sections} sections, {total_items} items")

                return True
            else:
                if self.debug:
                    print(f"[WARNING] Dictionary file not found: {self.dictionary_path}")
                self._create_default_dictionary()
                return True

        except Exception as e:
            if self.debug:
                print(f"[ERROR] Error loading dictionary from file: {e}")
            self._create_default_dictionary()
            return False

    def save_dictionary(self) -> bool:
        """
        Save the current dictionary to database or file.

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if self.use_database and self.database:
                # Save to SQLite database
                return self._save_to_database()
            else:
                # Save to JSON file (fallback)
                raise Exception("JSON fallback disabled - database required")

        except Exception as e:
            if self.debug:
                print(f"[ERROR] Error saving dictionary: {e}")
            return False

    def _save_to_database(self) -> bool:
        """Save dictionary to SQLite database using JSON storage"""
        try:
            import json

            # Convert dictionary to JSON string for storage
            dictionary_json = json.dumps(self.dictionary, indent=2)

            # Use the database's execute_update method to save
            # First, create the table if it doesn't exist
            self.database.execute_update('''
                CREATE TABLE IF NOT EXISTS payroll_dictionary (
                    id INTEGER PRIMARY KEY,
                    dictionary_data TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Clear existing data and insert new
            self.database.execute_update('DELETE FROM payroll_dictionary')
            self.database.execute_update(
                'INSERT INTO payroll_dictionary (dictionary_data) VALUES (?)',
                (dictionary_json,)
            )

            if self.debug:
                sections = len(self.dictionary)
                total_items = sum(len(section.get('items', {})) for section in self.dictionary.values() if isinstance(section, dict))
                print(f"[+] Dictionary saved to database: {sections} sections, {total_items} items")

            return True

        except Exception as e:
            if self.debug:
                print(f"[ERROR] Database save failed: {e}")
            raise Exception("Database connection required - JSON fallback disabled")

    def _save_to_file(self) -> bool:
        """Save dictionary to JSON file (fallback)"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.dictionary_path), exist_ok=True)

            with open(self.dictionary_path, 'w', encoding='utf-8') as f:
                json.dump(self.dictionary, f, indent=2, ensure_ascii=False)

            if self.debug:
                print(f"[+] Dictionary saved to file: {self.dictionary_path}")

            return True

        except Exception as e:
            if self.debug:
                print(f"[ERROR] Error saving dictionary to file: {e}")
            return False

    def standardize_label(self, section_name: str, raw_label: str) -> str:
        """
        Standardize a raw extracted label to its dictionary standard name.

        Args:
            section_name: Name of the section
            raw_label: Raw label extracted from payslip

        Returns:
            Standardized label name
        """
        try:
            section = self.dictionary.get(section_name, {})
            items = section.get('items', {})

            # First check if it's already a standard name
            if raw_label in items:
                return items[raw_label].get('standardized_name', raw_label)

            # Check variations/aliases
            for item_name, item_data in items.items():
                variations = item_data.get('variations', [])
                if raw_label.upper() in [v.upper() for v in variations]:
                    return item_data.get('standardized_name', item_name)

            # If not found, return the raw label (will be added to dictionary)
            return raw_label

        except Exception as e:
            if self.debug:
                print(f"⚠️ Error standardizing label {section_name}.{raw_label}: {e}")
            return raw_label

    def add_extracted_item(self, section_name: str, raw_label: str, value: str) -> str:
        """
        Add a newly extracted item to the dictionary for user approval.

        Args:
            section_name: Name of the section
            raw_label: Raw label extracted from payslip
            value: Extracted value

        Returns:
            Standardized label name
        """
        try:
            # Check if item already exists
            standardized = self.standardize_label(section_name, raw_label)

            if standardized == raw_label:
                # New item - add to dictionary for user approval
                item_data = {
                    "format": self._detect_format(value),
                    "value_format": self._detect_value_format(value),
                    "include_in_report": True,  # Default to include
                    "standardized_name": raw_label,
                    "variations": [raw_label],
                    "needs_approval": True  # Flag for user review
                }

                self.add_item(section_name, raw_label, item_data)

                if self.debug:
                    print(f"📝 New item added for approval: {section_name}.{raw_label}")

            return standardized

        except Exception as e:
            print(f"❌ Error adding extracted item {section_name}.{raw_label}: {e}")
            return raw_label

    def classify_loan_type(self, loan_name: str) -> str:
        """
        Classify loan type based on dictionary (no hardcoded keywords).

        BUSINESS RULE:
        1. Check loan_types section first (explicit classifications)
        2. Check fixed items in LOANS section
        3. Default to EXTERNAL if not found

        Args:
            loan_name: Name of the loan

        Returns:
            'IN-HOUSE LOAN' or 'EXTERNAL LOAN'
        """
        # Check if this is a column header (not a loan type)
        fixed_loan_subsections = {"LOAN", "BALANCE B/F", "CURRENT DEDUCTION", "OUST. BALANCE"}
        if loan_name.upper() in fixed_loan_subsections:
            return "COLUMN_HEADER"  # Not a loan type, it's a column header

        loan_name_upper = loan_name.upper()

        # PRIORITY 1: Check explicit loan_types section
        loans_section = self.dictionary.get('LOANS', {})
        loan_types = loans_section.get('loan_types', {})

        for loan_type_name, loan_data in loan_types.items():
            loan_type_upper = loan_type_name.upper()
            if (loan_type_upper in loan_name_upper or loan_name_upper in loan_type_upper or
                loan_type_upper == loan_name_upper):
                classification = loan_data.get('classification', 'EXTERNAL LOAN')
                return classification

        # PRIORITY 2: Check fixed items in LOANS section (legacy support)
        loans_items = loans_section.get('items', {})

        for item_name, item_data in loans_items.items():
            if item_data.get('is_fixed', False):
                # Skip column headers
                if not item_data.get('is_column_header', False):
                    if item_name.upper() in loan_name_upper or loan_name_upper in item_name.upper():
                        return "IN-HOUSE LOAN"

        # If not found in either section, classify as EXTERNAL
        return "EXTERNAL LOAN"

    # ========== ENHANCED LOAN TYPE MANAGEMENT METHODS ==========

    def create_loan_type(self, loan_type_name: str, classification: str = "IN-HOUSE LOAN") -> bool:
        """
        Create a new loan type with classification.

        Args:
            loan_type_name: Name of the loan type (e.g., "RENT ADVANCE")
            classification: "IN-HOUSE LOAN" or "EXTERNAL LOAN"

        Returns:
            True if created successfully, False otherwise
        """
        try:
            # Ensure LOANS section exists with loan_types structure
            if "LOANS" not in self.dictionary:
                self.dictionary["LOANS"] = {"loan_types": {}, "items": {}}

            if "loan_types" not in self.dictionary["LOANS"]:
                self.dictionary["LOANS"]["loan_types"] = {}

            # Create the loan type
            self.dictionary["LOANS"]["loan_types"][loan_type_name] = {
                "classification": classification,
                "items": [],
                "created_date": self._get_current_timestamp(),
                "is_active": True
            }

            if self.debug:
                print(f"✅ Created loan type: {loan_type_name} ({classification})")

            return True

        except Exception as e:
            print(f"❌ Error creating loan type {loan_type_name}: {e}")
            return False

    def add_item_to_loan_type(self, loan_type_name: str, item_name: str, column_type: str) -> bool:
        """
        Add a loan item to a specific loan type.

        Args:
            loan_type_name: Name of the loan type
            item_name: Full item name (e.g., "RENT ADVANCE - BALANCE B/F")
            column_type: Column type (e.g., "BALANCE B/F")

        Returns:
            True if added successfully, False otherwise
        """
        try:
            # Ensure loan type exists
            if not self.loan_type_exists(loan_type_name):
                # Auto-create loan type with default classification
                self.create_loan_type(loan_type_name, "IN-HOUSE LOAN")

            # Add item to loan type's items list
            if item_name not in self.dictionary["LOANS"]["loan_types"][loan_type_name]["items"]:
                self.dictionary["LOANS"]["loan_types"][loan_type_name]["items"].append(item_name)

            # Add item to main items dictionary with loan metadata
            item_data = {
                "format": "amount" if column_type != "LOAN NAME" else "text",
                "value_format": "currency" if column_type != "LOAN NAME" else "text",
                "include_in_report": True,
                "standardized_name": item_name,
                "loan_type": loan_type_name,
                "column_type": column_type,
                "variations": [item_name]
            }

            self.add_item("LOANS", item_name, item_data)

            if self.debug:
                print(f"✅ Added item to loan type: {loan_type_name}.{item_name}")

            return True

        except Exception as e:
            print(f"❌ Error adding item to loan type: {e}")
            return False

    def get_loan_types(self) -> Dict:
        """
        Get all loan types and their information.

        Returns:
            Dictionary of loan types
        """
        try:
            if "LOANS" in self.dictionary and "loan_types" in self.dictionary["LOANS"]:
                return self.dictionary["LOANS"]["loan_types"]
            return {}
        except Exception as e:
            print(f"❌ Error getting loan types: {e}")
            return {}

    def loan_type_exists(self, loan_type_name: str) -> bool:
        """
        Check if a loan type exists.

        Args:
            loan_type_name: Name of the loan type

        Returns:
            True if exists, False otherwise
        """
        loan_types = self.get_loan_types()
        return loan_type_name in loan_types

    def get_loan_type_classification(self, loan_type_name: str) -> str:
        """
        Get the classification of a loan type.

        Args:
            loan_type_name: Name of the loan type

        Returns:
            Classification string or "UNKNOWN"
        """
        loan_types = self.get_loan_types()
        if loan_type_name in loan_types:
            return loan_types[loan_type_name].get("classification", "UNKNOWN")
        return "UNKNOWN"

    def update_loan_type_classification(self, loan_type_name: str, new_classification: str) -> bool:
        """
        Update the classification of a loan type.

        Args:
            loan_type_name: Name of the loan type
            new_classification: New classification

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            if self.loan_type_exists(loan_type_name):
                self.dictionary["LOANS"]["loan_types"][loan_type_name]["classification"] = new_classification

                if self.debug:
                    print(f"✅ Updated loan type classification: {loan_type_name} → {new_classification}")

                return True
            else:
                print(f"❌ Loan type not found: {loan_type_name}")
                return False

        except Exception as e:
            print(f"❌ Error updating loan type classification: {e}")
            return False

    def extract_loan_type_from_item_name(self, item_name: str) -> str:
        """
        Extract loan type name from a composite item name.

        Args:
            item_name: Full item name (e.g., "RENT ADVANCE - BALANCE B/F")

        Returns:
            Loan type name (e.g., "RENT ADVANCE")
        """
        # Split on " - " and take the first part
        if " - " in item_name:
            return item_name.split(" - ")[0].strip()
        return item_name

    def extract_column_type_from_item_name(self, item_name: str) -> str:
        """
        Extract column type from a composite item name.

        Args:
            item_name: Full item name (e.g., "RENT ADVANCE - BALANCE B/F")

        Returns:
            Column type (e.g., "BALANCE B/F")
        """
        # Split on " - " and take the second part
        if " - " in item_name:
            return item_name.split(" - ", 1)[1].strip()
        return "UNKNOWN"

    def auto_group_loan_items(self) -> Dict:
        """
        Automatically group existing loan items by loan type.

        Returns:
            Dictionary with grouping results
        """
        try:
            results = {
                "grouped_items": 0,
                "new_loan_types": 0,
                "loan_types_created": []
            }

            if "LOANS" not in self.dictionary or "items" not in self.dictionary["LOANS"]:
                return results

            # Find items that look like composite loan items
            for item_name in self.dictionary["LOANS"]["items"]:
                if " - " in item_name:
                    loan_type = self.extract_loan_type_from_item_name(item_name)
                    column_type = self.extract_column_type_from_item_name(item_name)

                    # Skip if this is a column header
                    if self.dictionary["LOANS"]["items"][item_name].get("is_column_header", False):
                        continue

                    # Create loan type if it doesn't exist
                    if not self.loan_type_exists(loan_type):
                        self.create_loan_type(loan_type, "IN-HOUSE LOAN")  # Default classification
                        results["new_loan_types"] += 1
                        results["loan_types_created"].append(loan_type)

                    # Add item to loan type
                    self.add_item_to_loan_type(loan_type, item_name, column_type)
                    results["grouped_items"] += 1

            if self.debug:
                print(f"📊 Auto-grouping results: {results}")

            return results

        except Exception as e:
            print(f"❌ Error in auto-grouping: {e}")
            return {"error": str(e)}

    def get_loan_classification_summary(self) -> Dict:
        """
        Get a summary of loan classifications.

        Returns:
            Dictionary with classification statistics
        """
        try:
            summary = {
                "IN-HOUSE LOAN": {"count": 0, "types": []},
                "EXTERNAL LOAN": {"count": 0, "types": []},
                "UNCLASSIFIED": {"count": 0, "types": []}
            }

            loan_types = self.get_loan_types()
            for loan_type_name, loan_data in loan_types.items():
                classification = loan_data.get("classification", "UNCLASSIFIED")

                if classification in summary:
                    summary[classification]["count"] += 1
                    summary[classification]["types"].append(loan_type_name)
                else:
                    summary["UNCLASSIFIED"]["count"] += 1
                    summary["UNCLASSIFIED"]["types"].append(loan_type_name)

            return summary

        except Exception as e:
            print(f"❌ Error getting classification summary: {e}")
            return {}

    def _get_current_timestamp(self) -> str:
        """Get current timestamp as string."""
        from datetime import datetime
        return datetime.now().isoformat()

    def is_fixed_item(self, section_name: str, item_name: str) -> bool:
        """
        Check if an item is a fixed item that cannot be deleted.

        Args:
            section_name: Name of the section
            item_name: Name of the item

        Returns:
            True if item is fixed and cannot be deleted
        """
        try:
            section = self.dictionary.get(section_name, {})
            items = section.get('items', {})
            item_data = items.get(item_name, {})

            return item_data.get('is_fixed', False)

        except Exception as e:
            if self.debug:
                print(f"⚠️ Error checking if item is fixed {section_name}.{item_name}: {e}")
            return False

    def can_delete_item(self, section_name: str, item_name: str) -> bool:
        """
        Check if an item can be deleted (not a fixed item).

        Args:
            section_name: Name of the section
            item_name: Name of the item

        Returns:
            True if item can be deleted, False if it's a fixed item
        """
        return not self.is_fixed_item(section_name, item_name)

    def should_add_to_dictionary(self, section_name: str, item_name: str) -> bool:
        """
        Check if an extracted item should be added to dictionary.
        According to MOCK.txt: Fixed items should appear in pending approval
        but should not be added to dictionary.

        Args:
            section_name: Name of the section
            item_name: Name of the item

        Returns:
            True if item should be added to dictionary, False for fixed items
        """
        # Check if this is a fixed item that already exists
        if self.is_fixed_item(section_name, item_name):
            return False

        # Check if this is a variation of a fixed item
        section = self.dictionary.get(section_name, {})
        items = section.get('items', {})

        for existing_item, item_data in items.items():
            if item_data.get('is_fixed', False):
                variations = item_data.get('variations', [])
                if item_name.upper() in [v.upper() for v in variations]:
                    return False

        return True

    def get_fixed_items_summary(self) -> Dict:
        """
        Get summary of all fixed items in the dictionary.

        Returns:
            Dictionary with fixed items count per section
        """
        summary = {}

        for section_name, section_data in self.dictionary.items():
            items = section_data.get('items', {})
            fixed_count = sum(1 for item_data in items.values()
                            if item_data.get('is_fixed', False))

            if fixed_count > 0:
                summary[section_name] = {
                    'fixed_items_count': fixed_count,
                    'fixed_items': [item_name for item_name, item_data in items.items()
                                  if item_data.get('is_fixed', False)]
                }

        return summary

    def _detect_format(self, value: str) -> str:
        """Detect format type from value"""
        if value.replace(',', '').replace('.', '').isdigit():
            return "amount"
        elif any(char.isdigit() for char in value):
            return "alphanumeric"
        else:
            return "text"

    def _detect_value_format(self, value: str) -> str:
        """Detect value format from value"""
        if value.replace(',', '').replace('.', '').isdigit():
            return "currency"
        elif value.isdigit():
            return "numeric"
        else:
            return "text"

    def should_include_in_report(self, section_name: str, item_name: str) -> bool:
        """
        Check if an item should be included in reports.

        Args:
            section_name: Name of the section
            item_name: Name of the item

        Returns:
            True if item should be included in reports, False otherwise
        """
        try:
            section = self.dictionary.get(section_name, {})
            items = section.get('items', {})
            item = items.get(item_name, {})

            # Default to True if not specified
            return item.get('include_in_report', True)

        except Exception as e:
            if self.debug:
                print(f"⚠️ Error checking include_in_report for {section_name}.{item_name}: {e}")
            return True  # Default to including items

    def should_report_removed_item(self, section_name: str, item_name: str, context: Dict = None) -> Dict[str, Any]:
        """
        Determine if a REMOVED item should be included in reports using Dictionary intelligence.

        Args:
            section_name: Name of the section
            item_name: Name of the item that was removed
            context: Additional context (employee_count, amount, frequency, etc.)

        Returns:
            Dict with decision, reason, and confidence
        """
        try:
            # Get removed item settings from dictionary
            removed_settings = self.dictionary.get('removed_item_settings', {})

            if not removed_settings.get('enable_smart_filtering', False):
                return {'should_report': True, 'reason': 'Smart filtering disabled', 'confidence': 1.0}

            # Check item-specific rules first (highest priority)
            item_rules = removed_settings.get('item_rules', {})
            full_item_key = f"{section_name}.{item_name}"

            if full_item_key in item_rules:
                action = item_rules[full_item_key]
                return self._apply_removed_item_action(action, f"Specific rule for {full_item_key}")

            if item_name in item_rules:
                action = item_rules[item_name]
                return self._apply_removed_item_action(action, f"Specific rule for {item_name}")

            # Check pattern-based rules (medium priority)
            pattern_rules = removed_settings.get('pattern_rules', {})
            for pattern, action in pattern_rules.items():
                if self._matches_pattern(item_name, pattern):
                    return self._apply_removed_item_action(action, f"Pattern rule: {pattern}")

            # Apply smart analysis (low priority)
            if context:
                smart_result = self._analyze_removed_item_smart(item_name, section_name, context, removed_settings)
                if smart_result['confidence'] > 0.7:  # High confidence threshold
                    return smart_result

            # Default action
            default_action = removed_settings.get('default_action', 'ALWAYS_REPORT')
            return self._apply_removed_item_action(default_action, "Default action")

        except Exception as e:
            if self.debug:
                print(f"⚠️ Error checking removed item {section_name}.{item_name}: {e}")
            return {'should_report': True, 'reason': 'Error in analysis', 'confidence': 0.5}

    def _apply_removed_item_action(self, action: str, reason: str) -> Dict[str, Any]:
        """Apply the specified action for a removed item"""
        if action == 'ALWAYS_REPORT':
            return {'should_report': True, 'reason': reason, 'confidence': 1.0}
        elif action == 'NEVER_REPORT':
            return {'should_report': False, 'reason': reason, 'confidence': 1.0}
        else:  # ANALYZE
            return {'should_report': True, 'reason': f"{reason} (requires analysis)", 'confidence': 0.5}

    def _matches_pattern(self, item_name: str, pattern: str) -> bool:
        """Check if item name matches the pattern (supports wildcards)"""
        import re

        # Convert wildcard pattern to regex
        regex_pattern = pattern.replace('*', '.*').replace('?', '.')
        regex_pattern = f"^{regex_pattern}$"

        return bool(re.match(regex_pattern, item_name.upper(), re.IGNORECASE))

    def _analyze_removed_item_smart(self, item_name: str, section_name: str, context: Dict, settings: Dict) -> Dict[str, Any]:
        """Smart analysis of removed items using thresholds and context"""
        thresholds = settings.get('smart_thresholds', {})

        reasons = []
        confidence_factors = []

        # Frequency analysis
        frequency = context.get('frequency', 1.0)
        freq_threshold = thresholds.get('frequency_threshold', 0.3)
        if frequency < freq_threshold:
            reasons.append(f"Low frequency ({frequency:.1%} < {freq_threshold:.1%})")
            confidence_factors.append(0.8)

        # Employee impact analysis
        employee_impact = context.get('employee_impact_ratio', 1.0)
        emp_threshold = thresholds.get('employee_impact_threshold', 0.1)
        if employee_impact < emp_threshold:
            reasons.append(f"Low employee impact ({employee_impact:.1%} < {emp_threshold:.1%})")
            confidence_factors.append(0.7)

        # Amount significance
        amount = context.get('amount', float('inf'))
        amount_threshold = thresholds.get('amount_threshold', 1000)
        if amount < amount_threshold:
            reasons.append(f"Small amount ({amount} < {amount_threshold})")
            confidence_factors.append(0.6)

        # Calculate overall confidence
        if confidence_factors:
            avg_confidence = sum(confidence_factors) / len(confidence_factors)
            should_report = avg_confidence < 0.7  # If high confidence it's routine, don't report

            return {
                'should_report': should_report,
                'reason': f"Smart analysis: {'; '.join(reasons)}",
                'confidence': avg_confidence
            }

        # No smart factors detected, default to report
        return {'should_report': True, 'reason': 'No smart factors detected', 'confidence': 0.5}

    def initialize_removed_item_settings(self):
        """Initialize default removed item settings in dictionary"""
        if 'removed_item_settings' not in self.dictionary:
            self.dictionary['removed_item_settings'] = {
                'enable_smart_filtering': True,
                'default_action': 'ANALYZE',

                # Critical items that should ALWAYS be reported if removed
                'item_rules': {
                    'BASIC SALARY': 'ALWAYS_REPORT',
                    'GROSS SALARY': 'ALWAYS_REPORT',
                    'NET PAY': 'ALWAYS_REPORT',
                    'EMPLOYEE NO.': 'ALWAYS_REPORT',
                    'TAXABLE SALARY': 'ALWAYS_REPORT',

                    # Common one-time items that should NEVER be reported
                    'OVERTIME': 'NEVER_REPORT',
                    'BONUS': 'NEVER_REPORT',
                    'TRAVEL ALLOWANCE': 'NEVER_REPORT',
                    'SPECIAL DEDUCTION': 'NEVER_REPORT',
                    'TEMPORARY ALLOWANCE': 'NEVER_REPORT'
                },

                # Pattern-based rules for maximum coverage
                'pattern_rules': {
                    '*OVERTIME*': 'NEVER_REPORT',
                    '*BONUS*': 'NEVER_REPORT',
                    '*ALLOWANCE': 'NEVER_REPORT',
                    'SPECIAL*': 'NEVER_REPORT',
                    'TEMP*': 'NEVER_REPORT',
                    '*ONE TIME*': 'NEVER_REPORT',
                    '*TEMPORARY*': 'NEVER_REPORT',
                    '*EXTRA*': 'NEVER_REPORT'
                },

                # Smart analysis thresholds
                'smart_thresholds': {
                    'frequency_threshold': 0.3,
                    'employee_impact_threshold': 0.1,
                    'amount_threshold': 1000
                }
            }

            # Save the updated dictionary
            self.save_dictionary()

            if self.debug:
                print("✅ Initialized default removed item settings")

    def get_section_items(self, section_name: str) -> Dict:
        """
        Get all items for a specific section.

        Args:
            section_name: Name of the section

        Returns:
            Dictionary of items in the section
        """
        section = self.dictionary.get(section_name, {})
        return section.get('items', {})

    def add_item(self, section_name: str, item_name: str, item_data: Dict) -> bool:
        """
        Add or update an item in the dictionary.

        Args:
            section_name: Name of the section
            item_name: Name of the item
            item_data: Item data dictionary

        Returns:
            True if added successfully, False otherwise
        """
        try:
            # Ensure section exists
            if section_name not in self.dictionary:
                self.dictionary[section_name] = {'items': {}}

            # Ensure items key exists
            if 'items' not in self.dictionary[section_name]:
                self.dictionary[section_name]['items'] = {}

            # Add the item
            self.dictionary[section_name]['items'][item_name] = item_data

            if self.debug:
                print(f"✅ Added item: {section_name}.{item_name}")

            return True

        except Exception as e:
            print(f"❌ Error adding item {section_name}.{item_name}: {e}")
            return False

    def remove_item(self, section_name: str, item_name: str) -> bool:
        """
        Remove an item from the dictionary.
        PROTECTION: Fixed items cannot be deleted (MOCK.txt requirement).

        Args:
            section_name: Name of the section
            item_name: Name of the item

        Returns:
            True if removed successfully, False otherwise
        """
        try:
            # Check if item is fixed and cannot be deleted
            if self.is_fixed_item(section_name, item_name):
                if self.debug:
                    print(f"🔒 PROTECTED: Cannot delete fixed item {section_name}.{item_name}")
                return False

            section = self.dictionary.get(section_name, {})
            items = section.get('items', {})

            if item_name in items:
                del items[item_name]

                if self.debug:
                    print(f"✅ Removed item: {section_name}.{item_name}")

                return True
            else:
                if self.debug:
                    print(f"⚠️ Item not found: {section_name}.{item_name}")
                return False

        except Exception as e:
            print(f"❌ Error removing item {section_name}.{item_name}: {e}")
            return False

    def get_all_sections(self) -> List[str]:
        """
        Get list of all section names.

        Returns:
            List of section names
        """
        return list(self.dictionary.keys())

    def get_dictionary_stats(self) -> Dict:
        """
        Get statistics about the dictionary.

        Returns:
            Dictionary with statistics
        """
        stats = {
            'total_sections': len(self.dictionary),
            'sections': {},
            'total_items': 0,
            'items_included_in_report': 0,
            'items_excluded_from_report': 0
        }

        for section_name, section_data in self.dictionary.items():
            items = section_data.get('items', {})
            section_stats = {
                'total_items': len(items),
                'included_in_report': 0,
                'excluded_from_report': 0
            }

            for item_name, item_data in items.items():
                if item_data.get('include_in_report', True):
                    section_stats['included_in_report'] += 1
                    stats['items_included_in_report'] += 1
                else:
                    section_stats['excluded_from_report'] += 1
                    stats['items_excluded_from_report'] += 1

            stats['sections'][section_name] = section_stats
            stats['total_items'] += section_stats['total_items']

        return stats

    def _create_default_dictionary(self):
        """Create a default dictionary structure with hardcoded fixed sections"""
        self.dictionary = {
            "PERSONAL DETAILS": {
                "items": {
                    "EMPLOYEE NO.": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "EMPLOYEE NO.",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Employee No.", "Staff No.", "ID"]
                    },
                    "EMPLOYEE NAME": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "EMPLOYEE NAME",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Name", "Full Name", "Staff Name"]
                    },
                    "SSF NO.": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "SSF NO.",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["SSF Number", "Social Security"]
                    },
                    "GHANA CARD ID": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "GHANA CARD ID",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Ghana Card", "National ID"]
                    },
                    "SECTION": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "SECTION",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Section", "Unit"]
                    },
                    "DEPARTMENT": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "DEPARTMENT",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Dept", "Division"]
                    },
                    "JOB TITLE": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "JOB TITLE",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Position", "Role", "Title"]
                    }
                }
            },
            "EARNINGS": {
                "items": {
                    "BASIC SALARY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "BASIC SALARY",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Basic Pay", "Base Salary"]
                    },
                    "GROSS SALARY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "GROSS SALARY",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Gross Pay", "Total Earnings"]
                    },
                    "NET PAY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "NET PAY",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Net Salary", "Take Home", "Final Pay"]
                    }
                }
            },
            "DEDUCTIONS": {
                "items": {
                    "SSF EMPLOYEE": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "SSF EMPLOYEE",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["SSF Deduction", "Social Security"]
                    },
                    "INCOME TAX": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "INCOME TAX",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Tax", "PAYE"]
                    },
                    "TAXABLE SALARY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "TAXABLE SALARY",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Taxable Income", "Taxable Pay"]
                    },
                    "TITHES": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "TITHES",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Tithe", "Church Contribution"]
                    },
                    "TOTAL DEDUCTIONS": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "TOTAL DEDUCTIONS",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Total Deduction", "Deductions Total"]
                    }
                }
            },
            "LOANS": {
                # Enhanced structure to support loan type grouping
                "loan_types": {
                    # Example loan types - will be populated dynamically
                    # "RENT ADVANCE": {
                    #     "classification": "EXTERNAL LOAN",
                    #     "items": ["RENT ADVANCE - BALANCE B/F", "RENT ADVANCE - CURRENT DEDUCTION", "RENT ADVANCE - OUST. BALANCE"]
                    # }
                },
                "items": {
                    # Fixed column headers for tabular structure
                    "LOAN": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "LOAN",
                        "is_fixed": True,  # Fixed sub-section - cannot be deleted
                        "is_column_header": True,  # Identifies this as a column header
                        "variations": ["Loan Type", "Loan Description", "LOAN NAME"]
                    },
                    "BALANCE B/F": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "BALANCE B/F",
                        "is_fixed": True,  # Fixed sub-section - cannot be deleted
                        "is_column_header": True,  # Identifies this as a column header
                        "variations": ["Balance Brought Forward", "Previous Balance"]
                    },
                    "CURRENT DEDUCTION": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "CURRENT DEDUCTION",
                        "is_fixed": True,  # Fixed sub-section - cannot be deleted
                        "is_column_header": True,  # Identifies this as a column header
                        "variations": ["Monthly Deduction", "Current Payment"]
                    },
                    "OUST. BALANCE": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "OUST. BALANCE",
                        "is_fixed": True,  # Fixed sub-section - cannot be deleted
                        "is_column_header": True,  # Identifies this as a column header
                        "variations": ["Outstanding Balance", "Remaining Balance", "OUTSTANDING BALANCE"]
                    }
                    # Dynamic loan items will be added here with loan_type metadata
                    # Example: "RENT ADVANCE - BALANCE B/F": {
                    #     "format": "amount",
                    #     "value_format": "currency",
                    #     "include_in_report": True,
                    #     "loan_type": "RENT ADVANCE",
                    #     "column_type": "BALANCE B/F",
                    #     "standardized_name": "RENT ADVANCE - BALANCE B/F"
                    # }
                }
            },
            "EMPLOYERS CONTRIBUTION": {
                "items": {
                    "SSF EMPLOYER": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "SSF EMPLOYER",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["SSF Contribution", "Employer SSF"]
                    },
                    "SAVING SCHEME EMPLOYER": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "SAVING SCHEME EMPLOYER",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Savings Scheme", "Employer Savings"]
                    }
                }
            },
            "EMPLOYEE BANK DETAILS": {
                "items": {
                    "BANK": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "BANK",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Bank Name", "Financial Institution"]
                    },
                    "ACCOUNT NO.": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "ACCOUNT NO.",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Account Number", "Bank Account"]
                    },
                    "BRANCH": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "BRANCH",
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "variations": ["Bank Branch", "Branch Name"]
                    }
                }
            }
        }

        # Initialize fixed IN-HOUSE loan types from user's original list
        self._initialize_fixed_loan_types()

        # Save the default dictionary
        self.save_dictionary()

    def _initialize_fixed_loan_types(self):
        """Initialize fixed IN-HOUSE loan types from user's original loan list"""
        # User's original loan list (IN-HOUSE loans) - Updated with corrections
        fixed_in_house_loans = [
            "SALARY ADVANCE-MINS",
            "BUILDING-MINISTERS",
            "SALARY ADVANCE-STAFF",
            "RENT ADVANCE",
            "SALARY ADVANCE MISSI",
            "SALARY ADVANCE MISSIONS",  # Fixed typo: was "SALARY ADVNACE MISSIONS"
            "RENT ADVANCE MISSIONS",
            "STAFF CREDIT UNION LO",    # Corrected: was "STAFF CREDIT UNION LOAN"
            "PENSIONS SALARY ADVA",
            "PENSIONS RENT ADVANCE"
        ]

        # Ensure LOANS section exists
        if "LOANS" not in self.dictionary:
            self.dictionary["LOANS"] = {"loan_types": {}, "items": {}}

        if "loan_types" not in self.dictionary["LOANS"]:
            self.dictionary["LOANS"]["loan_types"] = {}

        # Add each loan type as fixed IN-HOUSE loan
        for loan_type in fixed_in_house_loans:
            if loan_type not in self.dictionary["LOANS"]["loan_types"]:
                self.dictionary["LOANS"]["loan_types"][loan_type] = {
                    "classification": "IN-HOUSE LOAN",
                    "items": [],
                    "created_date": self._get_current_timestamp(),
                    "is_active": True,
                    "is_fixed": True,  # Mark as fixed (cannot be deleted)
                    "source": "USER_ORIGINAL_LIST"  # Track source
                }

                if self.debug:
                    print(f"🏠 Added fixed IN-HOUSE loan type: {loan_type}")

        # Also add these as fixed items in the LOANS section for classification
        if "items" not in self.dictionary["LOANS"]:
            self.dictionary["LOANS"]["items"] = {}

        # Create loan items for each loan type with standard columns
        loan_columns = ["BALANCE B/F", "CURRENT DEDUCTION", "OUST. BALANCE"]

        for loan_type in fixed_in_house_loans:
            for column in loan_columns:
                item_name = f"{loan_type} - {column}"
                if item_name not in self.dictionary["LOANS"]["items"]:
                    self.dictionary["LOANS"]["items"][item_name] = {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": item_name,
                        "is_fixed": True,  # Fixed item - cannot be deleted
                        "loan_type": loan_type,
                        "column_type": column,
                        "loan_classification": "IN-HOUSE LOAN",
                        "variations": [item_name]
                    }

        if self.debug:
            print(f"🏠 Initialized {len(fixed_in_house_loans)} fixed IN-HOUSE loan types")

# Utility functions for external use
def load_payroll_dictionary(dictionary_path: str = None) -> PayrollDictionaryManager:
    """
    Utility function to load the payroll dictionary.

    Args:
        dictionary_path: Optional path to dictionary file

    Returns:
        PayrollDictionaryManager instance
    """
    return PayrollDictionaryManager(dictionary_path)

def check_include_in_report(section_name: str, item_name: str, dictionary_path: str = None) -> bool:
    """
    Utility function to check if an item should be included in reports.

    Args:
        section_name: Name of the section
        item_name: Name of the item
        dictionary_path: Optional path to dictionary file

    Returns:
        True if item should be included in reports, False otherwise
    """
    manager = PayrollDictionaryManager(dictionary_path)
    return manager.should_include_in_report(section_name, item_name)

# Test function
def test_dictionary_manager():
    """Test the dictionary manager functionality"""
    print("🧪 THE PAYROLL AUDITOR - DICTIONARY MANAGER TEST")
    print("=" * 60)

    # Create manager
    manager = PayrollDictionaryManager()

    # Get statistics
    stats = manager.get_dictionary_stats()
    print("📊 Dictionary Statistics:")
    print(f"   Total sections: {stats['total_sections']}")
    print(f"   Total items: {stats['total_items']}")
    print(f"   Items included in reports: {stats['items_included_in_report']}")
    print(f"   Items excluded from reports: {stats['items_excluded_from_report']}")

    # Test include_in_report functionality
    print(f"\n🔍 Testing include_in_report:")
    test_items = [
        ("EARNINGS", "BASIC SALARY"),
        ("DEDUCTIONS", "TOTAL DEDUCTIONS"),
        ("PERSONAL DETAILS", "EMPLOYEE NO.")
    ]

    for section, item in test_items:
        include = manager.should_include_in_report(section, item)
        print(f"   {section}.{item}: {'✅ INCLUDE' if include else '❌ EXCLUDE'}")

    # Test fixed items protection
    print(f"\n🔒 Testing Fixed Items Protection:")
    test_fixed_items = [
        ("PERSONAL DETAILS", "EMPLOYEE NO."),
        ("EARNINGS", "BASIC SALARY"),
        ("LOANS", "LOAN NAME")
    ]

    for section, item in test_fixed_items:
        is_fixed = manager.is_fixed_item(section, item)
        can_delete = manager.can_delete_item(section, item)
        print(f"   {section}.{item}: {'🔒 FIXED' if is_fixed else '🔓 DYNAMIC'} | {'❌ PROTECTED' if not can_delete else '✅ DELETABLE'}")

    # Test loan classification
    print(f"\n🏦 Testing Loan Classification:")
    test_loans = [
        "STAFF LOAN",
        "VEHICLE LOAN",
        "LOAN NAME",  # This is a fixed subsection
        "BALANCE B/F"  # This is a fixed subsection
    ]

    for loan in test_loans:
        classification = manager.classify_loan_type(loan)
        print(f"   {loan}: {classification}")

    # Test fixed items summary
    print(f"\n📋 Fixed Items Summary:")
    fixed_summary = manager.get_fixed_items_summary()
    for section, data in fixed_summary.items():
        print(f"   {section}: {data['fixed_items_count']} fixed items")
        for item in data['fixed_items']:
            print(f"      - {item}")

    return manager

def main():
    """Command-line interface for dictionary manager operations"""
    import sys
    import json

    if len(sys.argv) < 2:
        print("Usage: python dictionary_manager.py <command> [args...]")
        sys.exit(1)

    command = sys.argv[1]
    manager = PayrollDictionaryManager(debug=False)

    try:
        if command == "create-loan-type":
            if len(sys.argv) != 4:
                print("Usage: create-loan-type <loan_type_name> <classification>")
                sys.exit(1)

            loan_type_name = sys.argv[2]
            classification = sys.argv[3]
            success = manager.create_loan_type(loan_type_name, classification)
            if success:
                manager.save_dictionary()
            print("true" if success else "false")

        elif command == "update-loan-classification":
            if len(sys.argv) != 4:
                print("Usage: update-loan-classification <loan_type_name> <classification>")
                sys.exit(1)

            loan_type_name = sys.argv[2]
            classification = sys.argv[3]
            success = manager.update_loan_type_classification(loan_type_name, classification)
            if success:
                manager.save_dictionary()
            print("true" if success else "false")

        elif command == "delete-loan-type":
            if len(sys.argv) != 3:
                print("Usage: delete-loan-type <loan_type_name>")
                sys.exit(1)

            loan_type_name = sys.argv[2]
            success = manager.delete_loan_type(loan_type_name)
            if success:
                manager.save_dictionary()
            print("true" if success else "false")

        elif command == "get-loan-types":
            loan_types = manager.get_loan_types()
            print(json.dumps(loan_types))

        elif command == "get-classification-summary":
            summary = manager.get_loan_classification_summary()
            print(json.dumps(summary))

        elif command == "auto-group-loans":
            results = manager.auto_group_loan_items()
            if "error" not in results:
                manager.save_dictionary()
            print(json.dumps(results))

        elif command == "add-item-to-loan-type":
            if len(sys.argv) != 5:
                print("Usage: add-item-to-loan-type <loan_type_name> <item_name> <column_type>")
                sys.exit(1)

            loan_type_name = sys.argv[2]
            item_name = sys.argv[3]
            column_type = sys.argv[4]
            success = manager.add_item_to_loan_type(loan_type_name, item_name, column_type)
            if success:
                manager.save_dictionary()
            print("true" if success else "false")

        elif command == "remove-item-from-loan-type":
            if len(sys.argv) != 4:
                print("Usage: remove-item-from-loan-type <loan_type_name> <item_name>")
                sys.exit(1)

            loan_type_name = sys.argv[2]
            item_name = sys.argv[3]
            success = manager.remove_item_from_loan_type(loan_type_name, item_name)
            if success:
                manager.save_dictionary()
            print("true" if success else "false")

        elif command == "detect-ungrouped-items":
            ungrouped_items = manager.detect_ungrouped_loan_items()
            print(json.dumps(ungrouped_items))

        elif command == "export-classification-report":
            if len(sys.argv) != 4:
                print("Usage: export-classification-report <format> <output_path>")
                sys.exit(1)

            format_type = sys.argv[2]
            output_path = sys.argv[3]
            success = manager.export_classification_report(format_type, output_path)
            print("true" if success else "false")

        elif command == "initialize-fixed-loans":
            # Initialize fixed IN-HOUSE loan types
            manager._initialize_fixed_loan_types()
            manager.save_dictionary()
            print("Fixed IN-HOUSE loan types initialized successfully")

        elif command == "test":
            # Run the test function
            test_dictionary_manager()

        else:
            print(f"Unknown command: {command}")
            sys.exit(1)

    except Exception as e:
        print(f"Error executing command '{command}': {e}")
        sys.exit(1)

def delete_loan_type(self, loan_type_name: str) -> bool:
    """
    Delete a loan type and all its associated items.

    Args:
        loan_type_name: Name of the loan type to delete

    Returns:
        True if deleted successfully, False otherwise
    """
    try:
        if not self.loan_type_exists(loan_type_name):
            print(f"❌ Loan type not found: {loan_type_name}")
            return False

        # Get the items associated with this loan type
        loan_types = self.get_loan_types()
        items_to_remove = loan_types[loan_type_name].get("items", [])

        # Remove all associated items from the main items dictionary
        for item_name in items_to_remove:
            if "LOANS" in self.dictionary and "items" in self.dictionary["LOANS"]:
                if item_name in self.dictionary["LOANS"]["items"]:
                    del self.dictionary["LOANS"]["items"][item_name]

        # Remove the loan type itself
        del self.dictionary["LOANS"]["loan_types"][loan_type_name]

        if self.debug:
            print(f"✅ Deleted loan type: {loan_type_name} and {len(items_to_remove)} associated items")

        return True

    except Exception as e:
        print(f"❌ Error deleting loan type {loan_type_name}: {e}")
        return False

def remove_item_from_loan_type(self, loan_type_name: str, item_name: str) -> bool:
    """
    Remove an item from a loan type.

    Args:
        loan_type_name: Name of the loan type
        item_name: Name of the item to remove

    Returns:
        True if removed successfully, False otherwise
    """
    try:
        if not self.loan_type_exists(loan_type_name):
            print(f"❌ Loan type not found: {loan_type_name}")
            return False

        # Remove from loan type's items list
        loan_types = self.get_loan_types()
        if item_name in loan_types[loan_type_name]["items"]:
            loan_types[loan_type_name]["items"].remove(item_name)

        # Remove from main items dictionary
        if "LOANS" in self.dictionary and "items" in self.dictionary["LOANS"]:
            if item_name in self.dictionary["LOANS"]["items"]:
                del self.dictionary["LOANS"]["items"][item_name]

        if self.debug:
            print(f"✅ Removed item from loan type: {loan_type_name}.{item_name}")

        return True

    except Exception as e:
        print(f"❌ Error removing item from loan type: {e}")
        return False

def detect_ungrouped_loan_items(self) -> List[Dict]:
    """
    Detect loan items that are not grouped under any loan type.

    Returns:
        List of ungrouped loan items with metadata
    """
    try:
        ungrouped_items = []

        if "LOANS" not in self.dictionary or "items" not in self.dictionary["LOANS"]:
            return ungrouped_items

        # Get all items that are associated with loan types
        loan_types = self.get_loan_types()
        grouped_items = set()
        for loan_data in loan_types.values():
            grouped_items.update(loan_data.get("items", []))

        # Find items that are not grouped and not column headers
        for item_name, item_data in self.dictionary["LOANS"]["items"].items():
            if (item_name not in grouped_items and
                not item_data.get("is_column_header", False) and
                " - " in item_name):  # Looks like a composite loan item

                loan_type = self.extract_loan_type_from_item_name(item_name)
                column_type = self.extract_column_type_from_item_name(item_name)

                ungrouped_items.append({
                    "item_name": item_name,
                    "suggested_loan_type": loan_type,
                    "column_type": column_type,
                    "format": item_data.get("format", "amount"),
                    "value_format": item_data.get("value_format", "currency")
                })

        return ungrouped_items

    except Exception as e:
        print(f"❌ Error detecting ungrouped items: {e}")
        return []

def export_classification_report(self, format_type: str, output_path: str) -> bool:
    """
    Export loan classification report to file.

    Args:
        format_type: Format type (xlsx, json, csv)
        output_path: Output file path

    Returns:
        True if exported successfully, False otherwise
    """
    try:
        summary = self.get_loan_classification_summary()
        loan_types = self.get_loan_types()

        if format_type.lower() == "json":
            import json
            report_data = {
                "summary": summary,
                "loan_types": loan_types,
                "generated_date": self._get_current_timestamp()
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            return True

        elif format_type.lower() == "csv":
            import csv

            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # Write header
                writer.writerow(["Loan Type", "Classification", "Items Count", "Items"])

                # Write data
                for loan_type_name, loan_data in loan_types.items():
                    classification = loan_data.get("classification", "UNCLASSIFIED")
                    items = loan_data.get("items", [])
                    items_str = "; ".join(items)

                    writer.writerow([loan_type_name, classification, len(items), items_str])

            return True

        elif format_type.lower() == "xlsx":
            try:
                import openpyxl
                from openpyxl.styles import Font, PatternFill

                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "Loan Classification Report"

                # Headers
                headers = ["Loan Type", "Classification", "Items Count", "Items"]
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                # Data
                row = 2
                for loan_type_name, loan_data in loan_types.items():
                    classification = loan_data.get("classification", "UNCLASSIFIED")
                    items = loan_data.get("items", [])
                    items_str = "; ".join(items)

                    ws.cell(row=row, column=1, value=loan_type_name)
                    ws.cell(row=row, column=2, value=classification)
                    ws.cell(row=row, column=3, value=len(items))
                    ws.cell(row=row, column=4, value=items_str)
                    row += 1

                # Auto-adjust column widths
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

                wb.save(output_path)
                return True

            except ImportError:
                print("❌ openpyxl not available for Excel export")
                return False

        else:
            print(f"❌ Unsupported format: {format_type}")
            return False

    except Exception as e:
        print(f"❌ Error exporting classification report: {e}")
        return False

# Add the missing methods to the PayrollDictionaryManager class
PayrollDictionaryManager.delete_loan_type = delete_loan_type
PayrollDictionaryManager.remove_item_from_loan_type = remove_item_from_loan_type
PayrollDictionaryManager.detect_ungrouped_loan_items = detect_ungrouped_loan_items
PayrollDictionaryManager.export_classification_report = export_classification_report

if __name__ == "__main__":
    main()
