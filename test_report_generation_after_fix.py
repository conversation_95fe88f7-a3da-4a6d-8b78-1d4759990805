#!/usr/bin/env python3
"""
Test report generation after fixing the missing audit_sessions table
"""

import sys
import os
import sqlite3
import json
import time
sys.path.append('.')

def test_report_generation_after_fix():
    """Test if report generation works after the fix"""
    
    print("=== TESTING REPORT GENERATION AFTER FIX ===")
    print()
    
    try:
        # 1. Create test session and data
        print("1. Creating test session and data:")

        # Use the correct database path that PhasedProcessManager uses
        import os
        db_path = os.path.join('data', 'templar_payroll_auditor.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create a test session
        test_session_id = f"test_session_{int(time.time())}"
        cursor.execute("""
            INSERT INTO audit_sessions 
            (session_id, current_pdf_path, previous_pdf_path, current_month, current_year, previous_month, previous_year, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_session_id, "test_current.pdf", "test_previous.pdf", "JUNE", "2025", "MAY", "2025", "in_progress"))
        
        print(f"   ✅ Created test session: {test_session_id}")
        
        # Create test comparison results (using correct column names)
        cursor.execute("""
            INSERT INTO comparison_results
            (session_id, employee_id, employee_name, section, item_label, previous_value, current_value, change_type, priority_level)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_session_id, "EMP001", "John Doe", "EARNINGS", "BASIC SALARY", "5000.00", "5500.00", "INCREASED", "HIGH"))
        
        change_id = cursor.lastrowid
        print(f"   ✅ Created test comparison result: {change_id}")
        
        # Create test pre-reporting result with selection (using correct column names)
        cursor.execute("""
            INSERT INTO pre_reporting_results
            (change_id, bulk_category, is_selected)
            VALUES (?, ?, ?)
        """, (change_id, "SALARY_INCREASES", 1))  # is_selected = 1
        
        print(f"   ✅ Created test pre-reporting result (selected for report)")
        
        conn.commit()
        conn.close()
        
        print()
        print("2. Testing report generation with test data:")
        
        # Test the actual report generation
        from core.phased_process_manager import PhasedProcessManager

        manager = PhasedProcessManager()
        manager.debug = True
        
        # Test generate_final_reports method
        result = manager.generate_final_reports(test_session_id)
        
        print(f"   📄 Report generation result: {json.dumps(result, indent=2)}")
        
        if result.get('success'):
            print("   ✅ Report generation SUCCESSFUL!")
            
            # Check if reports were actually created
            reports_dir = './reports'
            if os.path.exists(reports_dir):
                report_files = [f for f in os.listdir(reports_dir) if f.endswith(('.xlsx', '.docx', '.pdf'))]
                print(f"   📁 Found {len(report_files)} report files in ./reports/")
                
                # Show recent reports
                recent_reports = []
                for file in report_files:
                    file_path = os.path.join(reports_dir, file)
                    if os.path.getmtime(file_path) > time.time() - 300:  # Last 5 minutes
                        recent_reports.append(file)
                
                if recent_reports:
                    print("   📊 Recent reports generated:")
                    for report in recent_reports:
                        print(f"     - {report}")
                else:
                    print("   ⚠️ No recent reports found")
            else:
                print("   ❌ Reports directory doesn't exist")
        else:
            print(f"   ❌ Report generation FAILED: {result.get('error')}")
        
        print()
        print("3. Testing the complete flow (like UI would do):")
        
        # Test the complete flow that the UI uses
        print("   Step 1: Update selections in database")
        update_result = manager.update_pre_reporting_selections({
            'selectedChanges': [change_id]
        })
        print(f"   Update result: {update_result}")
        
        print("   Step 2: Generate final reports")
        final_result = manager.generate_final_reports(test_session_id)
        print(f"   Final result: {json.dumps(final_result, indent=2)}")
        
        print()
        print("4. Cleanup test data:")
        
        # Clean up test data
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM pre_reporting_results WHERE change_id = ?", (change_id,))
        cursor.execute("DELETE FROM comparison_results WHERE session_id = ?", (test_session_id,))
        cursor.execute("DELETE FROM audit_sessions WHERE session_id = ?", (test_session_id,))
        
        conn.commit()
        conn.close()
        
        print("   ✅ Test data cleaned up")
        
        print()
        if result.get('success') and final_result.get('success'):
            print("✅ REPORT GENERATION WORKS AFTER FIX!")
            print("The issue was indeed the missing audit_sessions table.")
        else:
            print("❌ REPORT GENERATION STILL HAS ISSUES")
            print("Further investigation needed.")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_report_generation_after_fix()
