/**
 * Python Worker Module
 * Provides worker thread functionality for non-blocking Python script execution
 */

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Create a Python worker that executes scripts in a separate thread
 * @param {string} scriptPath - Path to the Python script
 * @param {Array} args - Arguments for the Python script
 * @param {Object} options - Worker options
 * @returns {Promise} Promise that resolves with script result
 */
function createPythonWorker(scriptPath, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const {
      timeout = 30 * 60 * 1000, // 30 minutes default
      onProgress = () => {},
      onRealtimeUpdate = () => {}
    } = options;

    // Create worker thread
    const worker = new Worker(__filename, {
      workerData: {
        scriptPath,
        args,
        timeout
      }
    });

    let timeoutId = null;

    // Set up timeout
    if (timeout > 0) {
      timeoutId = setTimeout(() => {
        worker.terminate();
        reject(new Error(`Python worker timeout after ${timeout}ms`));
      }, timeout);
    }

    // Handle worker messages
    worker.on('message', (message) => {
      const { type, data } = message;

      switch (type) {
        case 'progress':
          onProgress(data);
          break;
        
        case 'realtime-update':
          onRealtimeUpdate(data);
          break;
        
        case 'result':
          if (timeoutId) clearTimeout(timeoutId);
          worker.terminate();
          resolve(data);
          break;
        
        case 'error':
          if (timeoutId) clearTimeout(timeoutId);
          worker.terminate();
          reject(new Error(data));
          break;
      }
    });

    // Handle worker errors
    worker.on('error', (error) => {
      if (timeoutId) clearTimeout(timeoutId);
      reject(error);
    });

    // Handle worker exit
    worker.on('exit', (code) => {
      if (timeoutId) clearTimeout(timeoutId);
      if (code !== 0) {
        reject(new Error(`Worker stopped with exit code ${code}`));
      }
    });
  });
}

// Worker thread execution
if (!isMainThread) {
  const { scriptPath, args, timeout } = workerData;

  // Execute Python script in worker thread
  executePythonScript(scriptPath, args)
    .then(result => {
      parentPort.postMessage({ type: 'result', data: result });
    })
    .catch(error => {
      parentPort.postMessage({ type: 'error', data: error.message });
    });
}

/**
 * Execute Python script with real-time output handling
 */
function executePythonScript(scriptPath, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`[PYTHON-WORKER] Executing: python ${scriptPath} ${args.join(' ')}`);

    const python = spawn('python', [scriptPath, ...args], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
        PYTHONUNBUFFERED: '1'
      }
    });

    let output = '';
    let errorOutput = '';

    // Handle stdout
    python.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;

      // Parse real-time updates
      const lines = text.split('\n');
      for (const line of lines) {
        if (line.trim().startsWith('REALTIME_UPDATE:')) {
          try {
            const updateData = JSON.parse(line.replace('REALTIME_UPDATE:', ''));
            parentPort.postMessage({ type: 'realtime-update', data: updateData });
          } catch (e) {
            // Ignore malformed JSON
          }
        } else if (line.trim().startsWith('PROGRESS:')) {
          try {
            const progressData = JSON.parse(line.replace('PROGRESS:', ''));
            parentPort.postMessage({ type: 'progress', data: progressData });
          } catch (e) {
            // Ignore malformed JSON
          }
        }
      }
    });

    // Handle stderr
    python.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // Handle process completion
    python.on('close', (code) => {
      if (code === 0) {
        // Try to parse output as JSON, fallback to raw text
        try {
          const result = JSON.parse(output.trim());
          resolve(result);
        } catch (e) {
          resolve(output.trim());
        }
      } else {
        reject(new Error(`Python script failed with code ${code}: ${errorOutput}`));
      }
    });

    // Handle process errors
    python.on('error', (error) => {
      reject(new Error(`Failed to start Python process: ${error.message}`));
    });
  });
}

module.exports = {
  createPythonWorker
};
