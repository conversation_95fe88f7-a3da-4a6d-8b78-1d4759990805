#!/usr/bin/env python3
"""
Bank Adviser Tracker Operations
Handles tracker table operations: clear table, delete records, duplicate checker
"""

import sys
import json
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for tracker operations"""
    
    if len(sys.argv) < 2:
        print(json.dumps({
            'success': False,
            'error': 'Command required. Available: clear_table, delete_records, check_duplicates'
        }))
        return
    
    command = sys.argv[1]
    
    try:
        from core.bank_adviser_tracker_manager import BankAdviserTrackerManager
        
        manager = BankAdviserTrackerManager(debug=False)
        
        if command == 'clear_table':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for clear_table'}
            else:
                category = sys.argv[2]
                result = manager.clear_table(category)
        
        elif command == 'delete_records':
            if len(sys.argv) < 4:
                result = {'success': False, 'error': 'Category and record IDs required for delete_records'}
            else:
                category = sys.argv[2]
                record_ids = [int(id_str) for id_str in sys.argv[3:]]
                result = manager.delete_records(category, record_ids)
        
        elif command == 'check_duplicates':
            year = int(sys.argv[2]) if len(sys.argv) > 2 else None
            from core.duplicate_checker import DuplicateChecker
            checker = DuplicateChecker(debug=False)
            result = checker.scan_all_tables_for_duplicates(year)
        
        elif command == 'get_tracker_data':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for get_tracker_data'}
            else:
                category = sys.argv[2]
                # Parse filters if provided
                filters = {}
                if len(sys.argv) > 3:
                    try:
                        filters = json.loads(sys.argv[3])
                    except:
                        pass
                result = manager.get_tracker_data(category, filters)

        elif command == 'populate_tables':
            result = populate_bank_adviser_tables()
        
        else:
            result = {'success': False, 'error': f'Unknown command: {command}'}
        
        print(json.dumps(result))
    
    except Exception as e:
        print(json.dumps({
            'success': False,
            'error': str(e),
            'command': command
        }))

def populate_bank_adviser_tables():
    """Populate Bank Adviser tables from tracker results"""
    import sqlite3
    import sys
    from pathlib import Path

    try:
        # CRITICAL FIX: Use correct database path
        db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
        if not db_path.exists():
            return {'success': False, 'error': 'Database file not found'}

        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # CRITICAL FIX: Get session with actual tracker data
        cursor.execute("""
            SELECT session_id, COUNT(*) as tracker_count
            FROM tracker_results
            GROUP BY session_id
            ORDER BY tracker_count DESC, session_id DESC
            LIMIT 1
        """)

        result = cursor.fetchone()
        if not result:
            return {'success': False, 'error': 'No tracker data found in any session'}

        current_session = result[0]
        tracker_count = result[1]

        print(f"Using session {current_session} with {tracker_count} tracker items", file=sys.stderr)

        # Clear existing data for this session
        tables_to_clear = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']

        for table in tables_to_clear:
            cursor.execute(f"DELETE FROM {table} WHERE source_session = ?", (current_session,))

        # Populate in-house loans
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, department, section
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
        """, (current_session,))

        in_house_data = cursor.fetchall()
        in_house_count = 0

        for row in in_house_data:
            try:
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()

                # Use department from tracker_results, fallback if null
                department = row[5] if row[5] and str(row[5]).strip() else 'Department not specified'

                cursor.execute("""
                    INSERT INTO in_house_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0], row[1], department, loan_type, row[4] or 0,
                    '06', '2025', '2025-06', current_session, 'Monitoring'
                ))
                in_house_count += 1
            except:
                pass

        # Populate external loans
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, department, section
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
        """, (current_session,))

        external_data = cursor.fetchall()
        external_count = 0

        for row in external_data:
            try:
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()

                # Use department from tracker_results, fallback if null
                department = row[5] if row[5] and str(row[5]).strip() else 'Department not specified'

                cursor.execute("""
                    INSERT INTO external_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0], row[1], department, loan_type, row[4] or 0,
                    '06', '2025', '2025-06', current_session
                ))
                external_count += 1
            except:
                pass

        # Populate motor vehicle maintenance
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, department, section
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
        """, (current_session,))

        motor_data = cursor.fetchall()
        motor_count = 0

        for row in motor_data:
            try:
                # Use department from tracker_results, fallback if null
                department = row[5] if row[5] and str(row[5]).strip() else 'Department not specified'

                cursor.execute("""
                    INSERT INTO motor_vehicle_maintenance
                    (employee_no, employee_name, department, allowance_type,
                     allowance_amount, payable_amount, maintenance_amount,
                     period_month, period_year, period_acquired,
                     source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0], row[1], department, row[2],
                    row[4] or 0, row[4] or 0, row[4] or 0,
                    '06', '2025', '2025-06', current_session, 'Monitoring'
                ))
                motor_count += 1
            except:
                pass

        conn.commit()
        conn.close()

        total = in_house_count + external_count + motor_count
        return {
            'success': True,
            'in_house_loans': in_house_count,
            'external_loans': external_count,
            'motor_vehicles': motor_count,
            'total': total
        }

    except Exception as e:
        return {'success': False, 'error': str(e)}

if __name__ == '__main__':
    main()
