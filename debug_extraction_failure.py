#!/usr/bin/env python3
"""
Debug the extraction phase failure
"""

import sys
import os
sys.path.append('.')

def debug_extraction_failure():
    """Debug what's causing the extraction phase to fail"""
    
    print("=== DEBUGGING EXTRACTION PHASE FAILURE ===")
    print()
    
    try:
        from core.python_database_manager import PythonDatabaseManager
        
        # Initialize database manager
        db_manager = PythonDatabaseManager()
        
        print("1. Checking audit_sessions table:")
        
        # Check if audit_sessions table has any data
        sessions = db_manager.execute_query('SELECT * FROM audit_sessions ORDER BY created_at DESC LIMIT 5')
        
        if not sessions:
            print("   ❌ No sessions found in audit_sessions table")
            print("   This is likely the root cause of the extraction failure")
            return
        
        print(f"   ✅ Found {len(sessions)} sessions")
        
        # Show latest session details
        latest_session = sessions[0]
        print(f"   Latest session ID: {latest_session.get('session_id')}")
        print(f"   Current PDF: {latest_session.get('current_pdf_path')}")
        print(f"   Previous PDF: {latest_session.get('previous_pdf_path')}")
        print(f"   Status: {latest_session.get('status')}")
        
        # Check if PDF files exist
        current_pdf = latest_session.get('current_pdf_path')
        previous_pdf = latest_session.get('previous_pdf_path')
        
        if current_pdf:
            if os.path.exists(current_pdf):
                print(f"   ✅ Current PDF exists: {current_pdf}")
            else:
                print(f"   ❌ Current PDF missing: {current_pdf}")
        else:
            print("   ❌ Current PDF path is None/empty")
        
        if previous_pdf:
            if os.path.exists(previous_pdf):
                print(f"   ✅ Previous PDF exists: {previous_pdf}")
            else:
                print(f"   ❌ Previous PDF missing: {previous_pdf}")
        else:
            print("   ❌ Previous PDF path is None/empty")
        
        print()
        print("2. Testing PerfectExtractionIntegrator import:")
        
        try:
            from core.perfect_extraction_integration import PerfectExtractionIntegrator
            print("   ✅ PerfectExtractionIntegrator imported successfully")
            
            # Test creating integrator
            integrator = PerfectExtractionIntegrator(debug=True)
            print("   ✅ PerfectExtractionIntegrator created successfully")
            
        except Exception as e:
            print(f"   ❌ PerfectExtractionIntegrator error: {e}")
            import traceback
            traceback.print_exc()
        
        print()
        print("3. Testing PyPDF2 and dependencies:")
        
        try:
            import PyPDF2
            print(f"   ✅ PyPDF2 version: {PyPDF2.__version__}")
        except Exception as e:
            print(f"   ❌ PyPDF2 error: {e}")
        
        try:
            import fitz
            print(f"   ✅ PyMuPDF (fitz) imported successfully")
        except Exception as e:
            print(f"   ❌ PyMuPDF (fitz) error: {e}")
        
        print()
        print("4. Testing with sample PDF (if available):")
        
        # Look for sample PDFs
        sample_pdfs = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.pdf'):
                    sample_pdfs.append(os.path.join(root, file))
                    if len(sample_pdfs) >= 2:  # We need 2 PDFs for testing
                        break
            if len(sample_pdfs) >= 2:
                break
        
        if len(sample_pdfs) >= 2:
            print(f"   Found sample PDFs: {sample_pdfs[:2]}")
            
            # Test extraction on first PDF
            try:
                integrator = PerfectExtractionIntegrator(debug=False)  # Reduce noise
                result = integrator.process_large_payroll(sample_pdfs[0])
                
                if result.get('success'):
                    print(f"   ✅ Sample extraction successful: {len(result.get('employees', []))} employees")
                else:
                    print(f"   ❌ Sample extraction failed: {result.get('error')}")
                    
            except Exception as e:
                print(f"   ❌ Sample extraction error: {e}")
        else:
            print("   ⚠️ No sample PDFs found for testing")
        
        print()
        print("✅ EXTRACTION DEBUGGING COMPLETED")
        
    except Exception as e:
        print(f"❌ DEBUG ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_extraction_failure()
