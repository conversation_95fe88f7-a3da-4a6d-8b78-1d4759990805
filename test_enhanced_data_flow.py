#!/usr/bin/env python3
"""
Phase 5: Testing and Verification
Test the complete data flow and verify all three tables receive proper department information
"""

import sys
import os
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_enhanced_data_flow():
    """Test the complete enhanced data flow"""
    print("🧪 PHASE 5: TESTING ENHANCED DATA FLOW")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"📁 Using database: {db_path}")
        
        # 1. Test tracker_results enhancement
        print("\n1. 🔍 TESTING TRACKER_RESULTS ENHANCEMENT:")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results")
        total_tracker = cursor.fetchone()[0]
        print(f"   Total tracker_results: {total_tracker}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NOT NULL")
        dept_tracker = cursor.fetchone()[0]
        print(f"   Records with department: {dept_tracker}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NULL")
        null_tracker = cursor.fetchone()[0]
        print(f"   Records without department: {null_tracker}")
        
        # Show department distribution
        cursor.execute("""
            SELECT department, COUNT(*) as count
            FROM tracker_results 
            WHERE department IS NOT NULL
            GROUP BY department
            ORDER BY count DESC
        """)
        dept_dist = cursor.fetchall()
        print("   Department distribution:")
        for dept, count in dept_dist:
            print(f"     {dept}: {count} records")
        
        # 2. Clear and repopulate Bank Adviser tables to test new flow
        print("\n2. 🔄 TESTING ENHANCED TRANSFER SCRIPT:")
        
        # Get current session with tracker data
        cursor.execute("""
            SELECT session_id, COUNT(*) as tracker_count
            FROM tracker_results
            GROUP BY session_id
            ORDER BY tracker_count DESC, session_id DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("   ❌ No tracker data found")
            return False
        
        current_session = result[0]
        tracker_count = result[1]
        print(f"   Using session: {current_session} ({tracker_count} tracker items)")
        
        # Clear existing Bank Adviser data for this session
        tables_to_clear = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in tables_to_clear:
            cursor.execute(f"DELETE FROM {table} WHERE source_session = ?", (current_session,))
            print(f"   Cleared {table}")
        
        # 3. Test In-House Loans Transfer
        print("\n3. 🏦 TESTING IN-HOUSE LOANS TRANSFER:")
        
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, department, section
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
            LIMIT 5
        """, (current_session,))
        
        in_house_data = cursor.fetchall()
        print(f"   Found {len(in_house_data)} in-house loans in tracker_results")
        
        in_house_count = 0
        for row in in_house_data:
            try:
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()
                
                # Use department from tracker_results, fallback if null
                department = row[5] if row[5] and str(row[5]).strip() else 'Department not specified'
                
                cursor.execute("""
                    INSERT INTO in_house_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0], row[1], department, loan_type, row[4] or 0,
                    '06', '2025', '2025-06', current_session, 'Monitoring'
                ))
                in_house_count += 1
                
                if in_house_count <= 3:
                    print(f"   ✅ {row[0]} - {loan_type}: {department}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print(f"   Inserted {in_house_count} in-house loans")
        
        # 4. Test External Loans Transfer
        print("\n4. 🏛️ TESTING EXTERNAL LOANS TRANSFER:")
        
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, department, section
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
            LIMIT 5
        """, (current_session,))
        
        external_data = cursor.fetchall()
        print(f"   Found {len(external_data)} external loans in tracker_results")
        
        external_count = 0
        for row in external_data:
            try:
                loan_type = row[2]  # item_label
                if ' - ' in loan_type:
                    loan_type = loan_type.split(' - ')[0].strip()
                
                # Use department from tracker_results, fallback if null
                department = row[5] if row[5] and str(row[5]).strip() else 'Department not specified'
                
                cursor.execute("""
                    INSERT INTO external_loans
                    (employee_no, employee_name, department, loan_type, loan_amount,
                     period_month, period_year, period_acquired, source_session)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0], row[1], department, loan_type, row[4] or 0,
                    '06', '2025', '2025-06', current_session
                ))
                external_count += 1
                
                if external_count <= 3:
                    print(f"   ✅ {row[0]} - {loan_type}: {department}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print(f"   Inserted {external_count} external loans")
        
        # 5. Test Motor Vehicle Maintenance Transfer
        print("\n5. 🚗 TESTING MOTOR VEHICLE MAINTENANCE TRANSFER:")
        
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value, department, section
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
            LIMIT 5
        """, (current_session,))
        
        motor_data = cursor.fetchall()
        print(f"   Found {len(motor_data)} motor vehicle items in tracker_results")
        
        motor_count = 0
        for row in motor_data:
            try:
                # Use department from tracker_results, fallback if null
                department = row[5] if row[5] and str(row[5]).strip() else 'Department not specified'
                
                cursor.execute("""
                    INSERT INTO motor_vehicle_maintenance
                    (employee_no, employee_name, department, allowance_type,
                     allowance_amount, payable_amount, maintenance_amount,
                     period_month, period_year, period_acquired,
                     source_session, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row[0], row[1], department, row[2],
                    row[4] or 0, row[4] or 0, row[4] or 0,
                    '06', '2025', '2025-06', current_session, 'Monitoring'
                ))
                motor_count += 1
                
                if motor_count <= 3:
                    print(f"   ✅ {row[0]} - {row[2]}: {department}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print(f"   Inserted {motor_count} motor vehicle records")
        
        # 6. Verify Final Results
        print("\n6. ✅ FINAL VERIFICATION:")
        
        # Check in_house_loans
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        final_in_house = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ? AND department != 'Department not specified'", (current_session,))
        in_house_with_dept = cursor.fetchone()[0]
        
        print(f"   In-house loans: {final_in_house} total, {in_house_with_dept} with proper department")
        
        # Check external_loans
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        final_external = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ? AND department != 'Department not specified'", (current_session,))
        external_with_dept = cursor.fetchone()[0]
        
        print(f"   External loans: {final_external} total, {external_with_dept} with proper department")
        
        # Check motor_vehicle_maintenance
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        final_motor = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ? AND department != 'Department not specified'", (current_session,))
        motor_with_dept = cursor.fetchone()[0]
        
        print(f"   Motor vehicle: {final_motor} total, {motor_with_dept} with proper department")
        
        # Show sample data from each table
        print("\n7. 📋 SAMPLE DATA FROM ENHANCED TABLES:")
        
        print("   In-house loans:")
        cursor.execute("SELECT employee_no, employee_name, department, loan_type FROM in_house_loans WHERE source_session = ? LIMIT 3", (current_session,))
        for row in cursor.fetchall():
            print(f"     {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        print("   External loans:")
        cursor.execute("SELECT employee_no, employee_name, department, loan_type FROM external_loans WHERE source_session = ? LIMIT 3", (current_session,))
        for row in cursor.fetchall():
            print(f"     {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        print("   Motor vehicle maintenance:")
        cursor.execute("SELECT employee_no, employee_name, department, allowance_type FROM motor_vehicle_maintenance WHERE source_session = ? LIMIT 3", (current_session,))
        for row in cursor.fetchall():
            print(f"     {row[0]} | {row[1]} | {row[2]} | {row[3]}")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ PHASE 5 COMPLETED SUCCESSFULLY")
        print(f"   - Enhanced tracker_results: {dept_tracker}/{total_tracker} with department")
        print(f"   - In-house loans: {in_house_with_dept}/{final_in_house} with proper department")
        print(f"   - External loans: {external_with_dept}/{final_external} with proper department")
        print(f"   - Motor vehicle: {motor_with_dept}/{final_motor} with proper department")
        print(f"   - Solution 1 implementation SUCCESSFUL!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_data_flow()
    sys.exit(0 if success else 1)
