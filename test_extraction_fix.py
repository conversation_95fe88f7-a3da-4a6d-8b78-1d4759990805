#!/usr/bin/env python3
"""
Test the extraction fix and batch size increase
"""

import sys
import os
sys.path.append('.')

def test_extraction_fix():
    """Test if the extraction phase works after the fix"""
    
    print("=== TESTING EXTRACTION FIX ===")
    print()
    
    try:
        from core.phased_process_manager import PhasedProcessManager
        
        # Initialize process manager
        manager = PhasedProcessManager()
        manager.debug_mode = True
        
        print("1. Testing session creation:")
        
        # Test session creation with sample PDFs
        current_pdf = "JULY FILE/JULY.pdf"
        previous_pdf = "JULY FILE/JUNE.pdf"
        
        if not os.path.exists(current_pdf):
            print(f"   ❌ Current PDF not found: {current_pdf}")
            return
        
        if not os.path.exists(previous_pdf):
            print(f"   ❌ Previous PDF not found: {previous_pdf}")
            return
        
        print(f"   ✅ PDFs found: {current_pdf}, {previous_pdf}")
        
        # Create session
        options = {
            'current_month': 'JULY',
            'current_year': '2025',
            'previous_month': 'JUNE',
            'previous_year': '2025'
        }
        
        session_id = manager.create_session(current_pdf, previous_pdf, options)
        print(f"   ✅ Session created: {session_id}")
        
        print()
        print("2. Testing extraction phase:")
        
        # Test extraction phase
        try:
            result = manager._phase_extraction(options)
            
            if result:
                print("   ✅ Extraction phase completed successfully!")
            else:
                print("   ❌ Extraction phase returned False")
                
        except Exception as e:
            print(f"   ❌ Extraction phase failed: {e}")
            import traceback
            traceback.print_exc()
        
        print()
        print("3. Testing batch size increase:")
        
        from core.perfect_extraction_integration import PerfectExtractionIntegrator
        integrator = PerfectExtractionIntegrator(debug=False)
        
        # Check if batch size is now 100
        import inspect
        sig = inspect.signature(integrator.process_large_payroll)
        batch_size_default = sig.parameters['batch_size'].default
        
        if batch_size_default == 100:
            print(f"   ✅ Batch size increased to {batch_size_default}")
        else:
            print(f"   ❌ Batch size is still {batch_size_default}, expected 100")
        
        print()
        print("✅ EXTRACTION FIX TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_extraction_fix()
