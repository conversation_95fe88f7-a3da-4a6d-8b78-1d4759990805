#!/usr/bin/env python3
"""
EXACT DIAGNOSIS: Why report generation fails from Pre-reporting UI
"""

import sys
import os
import sqlite3
sys.path.append('.')

def diagnose_report_generation():
    """Diagnose the exact report generation issue"""
    
    print("=== EXACT REPORT GENERATION DIAGNOSIS ===")
    print()
    
    try:
        # Check database directly
        db_path = 'payroll_audit.db'
        if not os.path.exists(db_path):
            print("❌ ISSUE FOUND: Database file doesn't exist")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("1. Checking Required Tables:")
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['comparison_results', 'pre_reporting_results', 'audit_sessions']
        for table in required_tables:
            if table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} records")
            else:
                print(f"   ❌ {table}: MISSING TABLE")
                return
        
        print()
        print("2. Checking Latest Session:")
        
        cursor.execute('SELECT session_id, status FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_data = cursor.fetchone()
        
        if not session_data:
            print("   ❌ ISSUE FOUND: No sessions in database")
            return
        
        session_id = session_data[0]
        session_status = session_data[1]
        print(f"   ✅ Latest session: {session_id}")
        print(f"   ✅ Session status: {session_status}")
        
        print()
        print("3. Checking Comparison Results:")
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
        comparison_count = cursor.fetchone()[0]
        print(f"   ✅ Comparison results: {comparison_count} records")
        
        if comparison_count == 0:
            print("   ❌ ISSUE FOUND: No comparison results for latest session")
            return
        
        print()
        print("4. Checking Pre-reporting Results:")
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results pr JOIN comparison_results cr ON pr.change_id = cr.id WHERE cr.session_id = ?', (session_id,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   ✅ Pre-reporting results: {pre_reporting_count} records")
        
        if pre_reporting_count == 0:
            print("   ❌ ISSUE FOUND: No pre-reporting results for latest session")
            return
        
        print()
        print("5. Checking Selected Changes:")
        
        cursor.execute('''
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN pr.selected_for_report = 1 THEN 1 ELSE 0 END) as selected
            FROM pre_reporting_results pr 
            JOIN comparison_results cr ON pr.change_id = cr.id 
            WHERE cr.session_id = ?
        ''', (session_id,))
        
        selection_data = cursor.fetchone()
        total_changes = selection_data[0]
        selected_changes = selection_data[1]
        
        print(f"   ✅ Total changes: {total_changes}")
        print(f"   ✅ Selected for report: {selected_changes}")
        
        if selected_changes == 0:
            print("   ❌ ISSUE FOUND: No changes selected for reporting")
            print("   This is why report generation returns 'No changes selected'")
            
            # Check if there are any selections at all
            cursor.execute('SELECT change_id, selected_for_report FROM pre_reporting_results LIMIT 5')
            sample_selections = cursor.fetchall()
            print("   Sample pre_reporting_results:")
            for change_id, selected in sample_selections:
                print(f"     change_id: {change_id}, selected_for_report: {selected}")
            
            return
        
        print()
        print("6. Testing Report Generation Query:")
        
        # Test the exact query used in _load_selected_changes_for_reporting
        cursor.execute('''
            SELECT cr.id, cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                   cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                   cr.numeric_difference, cr.percentage_change,
                   pr.bulk_category, pr.bulk_size
            FROM comparison_results cr
            JOIN pre_reporting_results pr ON cr.id = pr.change_id
            WHERE cr.session_id = ? AND pr.selected_for_report = 1
            ORDER BY cr.priority DESC, pr.bulk_category, cr.section_name, cr.employee_id
        ''', (session_id,))
        
        selected_for_report = cursor.fetchall()
        print(f"   ✅ Query returns: {len(selected_for_report)} selected changes")
        
        if len(selected_for_report) == 0:
            print("   ❌ ISSUE CONFIRMED: Query returns no results")
            print("   This is exactly why report generation fails")
        else:
            print("   ✅ Query works - report generation should succeed")
            print("   Sample selected change:")
            sample = selected_for_report[0]
            print(f"     Employee: {sample[2]}")
            print(f"     Section: {sample[3]}")
            print(f"     Item: {sample[4]}")
            print(f"     Change: {sample[7]}")
        
        conn.close()
        
        print()
        print("✅ DIAGNOSIS COMPLETE!")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_report_generation()
