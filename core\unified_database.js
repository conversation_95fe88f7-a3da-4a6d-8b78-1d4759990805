/**
 * UNIFIED DATABASE SYSTEM
 * Comprehensive SQLite integration for ALL TEMPLAR PAYROLL AUDITOR modules
 * Solves UI freezing, slowing, and flickering across all functional systems
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class UnifiedDatabase {
    constructor(dbPath = null) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'templar_payroll_auditor.db');
        this.db = null;
        this.isConnected = false;
        this.transactionQueue = [];
        this.isProcessingQueue = false;
        this.isInitialized = false;

        // Ensure data directory exists
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Initialize database immediately for faster startup
        this.initializeDatabase();
    }

    /**
     * Initialize SQLite database with optimized startup performance and improved reliability
     */
    async initializeDatabase() {
        if (this.isInitialized) {
            return Promise.resolve();
        }

        // Set a timeout to prevent hanging indefinitely
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Database initialization timed out')), 10000);
        });

        const initPromise = new Promise((resolve, reject) => {
            try {
                console.log('🔄 Opening database connection:', this.dbPath);
                
                // Check if the database file exists, if not create an empty one
                const dbDir = path.dirname(this.dbPath);
                if (!fs.existsSync(dbDir)) {
                    console.log('📁 Creating database directory');
                    fs.mkdirSync(dbDir, { recursive: true });
                }
                
                // Try to open the database with a safety timeout
                this.db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
                    if (err) {
                        console.error('❌ Database connection error:', err);
                        reject(err);
                        return;
                    }

                    console.log('✅ Database connection established');
                    this.isConnected = true;

                    // Apply PRAGMA settings one by one with error handling
                    const pragmaSettings = [
                        'PRAGMA journal_mode = WAL',
                        'PRAGMA synchronous = NORMAL',
                        'PRAGMA cache_size = 2000',  // Reduced from 20000 to prevent memory issues
                        'PRAGMA temp_store = MEMORY',
                        'PRAGMA mmap_size = 67108864'  // Reduced to 64MB from 256MB
                    ];

                    // Apply settings sequentially with error handling
                    this.db.serialize(() => {
                        pragmaSettings.forEach(pragma => {
                            this.db.run(pragma, [], err => {
                                if (err) console.warn(`PRAGMA setting failed: ${pragma}`, err.message);
                            });
                        });
                        
                        // Continue with table creation
                        console.log('🔄 Checking database tables...');
                        this.checkAndCreateTables()
                            .then(() => {
                                console.log('✅ Database initialization completed successfully');
                                this.isInitialized = true;
                                resolve();
                            })
                            .catch(err => {
                                console.error('❌ Table creation failed:', err);
                                reject(err);
                            });
                    });
                });
            } catch (error) {
                console.error('❌ Fatal database error:', error);
                reject(error);
            }
        });

        // Race between initialization and timeout
        return Promise.race([initPromise, timeoutPromise]);
    }

    /**
     * Check if tables exist and create only if needed with improved reliability
     */
    async checkAndCreateTables() {
        try {
            console.log('🔍 Checking if database tables exist...');
            
            // Use a direct approach to check if the main table exists
            const exists = await new Promise((resolve) => {
                this.db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='dictionary_sections'", (err, row) => {
                    if (err) {
                        console.warn('⚠️ Table check error:', err.message);
                        resolve(false);
                        return;
                    }
                    resolve(!!row);
                });
            });

            if (exists) {
                // Tables exist, skip creation for faster startup
                console.log('✅ Database tables already exist - using them');
                return;
            } else {
                // Tables don't exist, create them
                console.log('📁 Database tables missing - creating new ones...');
                await this.createAllTables();
            }
        } catch (error) {
            console.error('❌ Error checking database tables:', error);
            
            // Attempt to create tables with more robust error handling
            try {
                console.log('🔄 Attempting to create database tables as fallback...');
                await this.createAllTables();
            } catch (createError) {
                // Critical failure
                console.error('❌❌ CRITICAL: Failed to create database tables:', createError);
                throw new Error('Critical database initialization failure: ' + createError.message);
            }
        }
    }

    /**
     * Create comprehensive database schema for all modules
     */
    async createAllTables() {
        const tables = [
            // ========== PAYROLL AUDIT TABLES ==========
            `CREATE TABLE IF NOT EXISTS payroll_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                pdf_path TEXT NOT NULL,
                total_pages INTEGER,
                total_employees INTEGER,
                processing_status TEXT DEFAULT 'pending',
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // CRITICAL: Add missing audit_sessions table that the entire codebase expects
            `CREATE TABLE IF NOT EXISTS audit_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                current_pdf_path TEXT,
                previous_pdf_path TEXT,
                current_month TEXT,
                current_year TEXT,
                previous_month TEXT,
                previous_year TEXT,
                status TEXT DEFAULT 'in_progress',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                stopped_at DATETIME,
                total_employees INTEGER DEFAULT 0,
                total_changes INTEGER DEFAULT 0
            )`,

            // CRITICAL FIX: Add missing audit_sessions table that Python code expects
            `CREATE TABLE IF NOT EXISTS audit_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                current_pdf_path TEXT,
                previous_pdf_path TEXT,
                current_month TEXT,
                current_year TEXT,
                previous_month TEXT,
                previous_year TEXT,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                total_employees INTEGER DEFAULT 0,
                total_changes INTEGER DEFAULT 0
            )`,

            `CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                section TEXT,
                job_title TEXT,
                ssf_number TEXT,
                ghana_card_id TEXT,
                page_number INTEGER,
                period_type TEXT DEFAULT 'current',
                extraction_confidence REAL DEFAULT 1.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES payroll_sessions(session_id)
            )`,

            `CREATE TABLE IF NOT EXISTS extracted_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_id TEXT,
                section_name TEXT,
                item_label TEXT,
                item_value TEXT,
                numeric_value REAL,
                value_format TEXT,
                period_type TEXT DEFAULT 'current',
                extraction_confidence REAL DEFAULT 1.0,
                extraction_source TEXT DEFAULT 'Perfect Section-Aware Extractor',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES payroll_sessions(session_id)
            )`,

            // ========== DICTIONARY MANAGER TABLES ==========
            `CREATE TABLE IF NOT EXISTS dictionary_sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT UNIQUE NOT NULL,
                section_order INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            `CREATE TABLE IF NOT EXISTS dictionary_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_id INTEGER,
                item_name TEXT NOT NULL,
                standard_key TEXT,
                format_type TEXT,
                value_format TEXT,
                include_in_report BOOLEAN DEFAULT 1,
                is_fixed BOOLEAN DEFAULT 0,
                validation_rules TEXT, -- JSON string
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (section_id) REFERENCES dictionary_sections(id),
                UNIQUE(section_id, item_name)
            )`,

            `CREATE TABLE IF NOT EXISTS loan_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                loan_name TEXT UNIQUE NOT NULL,
                classification TEXT CHECK(classification IN ('IN-HOUSE', 'EXTERNAL', 'UNSPECIFIED')),
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // ========== AUTO LEARNING TABLES ==========
            `CREATE TABLE IF NOT EXISTS auto_learning_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_name TEXT,
                session_id TEXT UNIQUE NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                ended_at DATETIME
            )`,

            `CREATE TABLE IF NOT EXISTS pending_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                item_label TEXT NOT NULL,
                suggested_section TEXT,
                suggested_standard_name TEXT,
                confidence_score REAL,
                occurrence_count INTEGER DEFAULT 1,
                status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'rejected')),
                rejection_reason TEXT,
                first_seen_in TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                processed_at DATETIME,
                FOREIGN KEY (session_id) REFERENCES auto_learning_sessions(session_id)
            )`,

            // ========== REPORT MANAGER TABLES ==========
            `CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_id TEXT UNIQUE NOT NULL,
                report_type TEXT NOT NULL,
                report_category TEXT, -- Payroll_Audit, PDF_Sorter, Data_Builder, etc.
                title TEXT NOT NULL,
                description TEXT,
                file_paths TEXT, -- JSON string of file paths
                metadata TEXT, -- JSON string of report metadata
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER,
                is_archived BOOLEAN DEFAULT 0
            )`,

            // ========== PDF SORTER TABLES ==========
            `CREATE TABLE IF NOT EXISTS pdf_sorting_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                original_pdf_path TEXT NOT NULL,
                sorted_pdf_path TEXT,
                sort_criteria TEXT, -- JSON string
                total_payslips INTEGER,
                processing_status TEXT DEFAULT 'pending',
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME
            )`,

            `CREATE TABLE IF NOT EXISTS sorted_payslips (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_id TEXT,
                employee_name TEXT,
                department TEXT,
                section TEXT,
                original_page INTEGER,
                sorted_position INTEGER,
                extraction_confidence REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES pdf_sorting_sessions(session_id)
            )`,

            // ========== DATA BUILDER TABLES ==========
            `CREATE TABLE IF NOT EXISTS data_builder_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                source_files TEXT, -- JSON array of file paths
                output_file_path TEXT,
                total_employees INTEGER,
                total_columns INTEGER,
                data_deficits INTEGER DEFAULT 0,
                processing_options TEXT, -- JSON string
                processing_status TEXT DEFAULT 'pending',
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME
            )`,

            `CREATE TABLE IF NOT EXISTS column_definitions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                column_name TEXT NOT NULL,
                original_label TEXT,
                section_name TEXT,
                format_type TEXT,
                value_type TEXT,
                include_in_report BOOLEAN DEFAULT 1,
                occurrence_count INTEGER DEFAULT 1,
                first_seen_employee TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES data_builder_sessions(session_id)
            )`,

            `CREATE TABLE IF NOT EXISTS data_deficits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_id TEXT,
                missing_field TEXT,
                expected_section TEXT,
                severity TEXT CHECK(severity IN ('critical', 'warning', 'info')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES data_builder_sessions(session_id)
            )`,

            // ========== PHASED PROCESS TABLES ==========
            `CREATE TABLE IF NOT EXISTS phased_process_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                current_pdf_path TEXT NOT NULL,
                previous_pdf_path TEXT,
                process_options TEXT, -- JSON string
                total_phases INTEGER DEFAULT 6,
                completed_phases INTEGER DEFAULT 0,
                current_phase TEXT,
                processing_status TEXT DEFAULT 'pending',
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            `CREATE TABLE IF NOT EXISTS phase_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                phase_name TEXT NOT NULL,
                phase_order INTEGER NOT NULL,
                success BOOLEAN NOT NULL,
                execution_time_seconds REAL,
                progress_percentage INTEGER DEFAULT 0,
                error_message TEXT,
                data_summary TEXT, -- JSON string with key metrics
                memory_usage_mb REAL,
                records_processed INTEGER,
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                FOREIGN KEY (session_id) REFERENCES phased_process_sessions(session_id),
                UNIQUE(session_id, phase_name)
            )`,

            `CREATE TABLE IF NOT EXISTS comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT CHECK(change_type IN ('NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED')),
                priority TEXT CHECK(priority IN ('High', 'Medium', 'Low', 'Routine')),
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES phased_process_sessions(session_id)
            )`,

            `CREATE TABLE IF NOT EXISTS tracker_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                tracker_type TEXT CHECK(tracker_type IN ('IN_HOUSE_LOAN', 'EXTERNAL_LOAN', 'MOTOR_VEHICLE')),
                item_label TEXT NOT NULL,
                item_value TEXT,
                numeric_value REAL,
                classification TEXT,
                detection_confidence REAL DEFAULT 1.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES phased_process_sessions(session_id)
            )`,

            `CREATE TABLE IF NOT EXISTS auto_learning_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                item_value TEXT,
                pattern_type TEXT,
                confidence_score REAL DEFAULT 1.0,
                learning_source TEXT DEFAULT 'current_month',
                dictionary_updated BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES phased_process_sessions(session_id)
            )`,

            `CREATE TABLE IF NOT EXISTS pre_reporting_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                change_id INTEGER NOT NULL,
                selected_for_report BOOLEAN DEFAULT 1,
                bulk_category TEXT CHECK(bulk_category IN ('Individual', 'Small_Bulk', 'Medium_Bulk', 'Large_Bulk')),
                bulk_size INTEGER DEFAULT 1,
                user_notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES phased_process_sessions(session_id)
            )`,

            // ========== SYSTEM PERFORMANCE TABLES ==========
            `CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                module_name TEXT NOT NULL,
                operation_type TEXT NOT NULL,
                execution_time_ms INTEGER,
                memory_usage_mb REAL,
                records_processed INTEGER,
                success_rate REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            `CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                module_name TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        // Run database migrations for existing tables
        await this.runMigrations();

        // Create indexes for performance optimization
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_employees_employee_id ON employees(employee_id)',
            'CREATE INDEX IF NOT EXISTS idx_employees_session_id ON employees(session_id)',
            'CREATE INDEX IF NOT EXISTS idx_employees_period_type ON employees(period_type)',
            'CREATE INDEX IF NOT EXISTS idx_extracted_items_employee_id ON extracted_items(employee_id)',
            'CREATE INDEX IF NOT EXISTS idx_extracted_items_section ON extracted_items(section_name)',
            'CREATE INDEX IF NOT EXISTS idx_extracted_items_period_type ON extracted_items(period_type)',
            'CREATE INDEX IF NOT EXISTS idx_pending_items_status ON pending_items(status)',
            'CREATE INDEX IF NOT EXISTS idx_reports_category ON reports(report_category)',
            'CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports(created_at)',
            'CREATE INDEX IF NOT EXISTS idx_performance_module ON performance_metrics(module_name)',
            'CREATE INDEX IF NOT EXISTS idx_performance_created_at ON performance_metrics(created_at)'
        ];

        // Execute table creation with batching for better performance
        const allQueries = [...tables, ...indexes];

        // Use transaction for faster bulk creation
        await this.executeTransaction(
            allQueries.map(sql => ({ sql, params: [] }))
        );
    }

    /**
     * Execute a query with parameters and timeout protection
     */
    runQuery(sql, params = [], options = {}) {
        const timeout = options.timeout || 5000; // 5 second default timeout
        
        return new Promise((resolve, reject) => {
            // Create timeout handler
            const timeoutId = setTimeout(() => {
                console.error(`⚠️ Query timeout after ${timeout}ms:`, sql.substring(0, 100));
                reject(new Error(`Database query timed out after ${timeout}ms`));
            }, timeout);
            
            try {
                this.db.run(sql, params, function(err) {
                    // Clear timeout since query completed
                    clearTimeout(timeoutId);
                    
                    if (err) {
                        console.error('❌ Database query error:', err.message);
                        console.error('   SQL:', sql.substring(0, 150));
                        console.error('   Params:', JSON.stringify(params).substring(0, 100));
                        reject(err);
                    } else {
                        resolve({ lastID: this.lastID, changes: this.changes });
                    }
                });
            } catch (error) {
                // Clear timeout and handle unexpected errors
                clearTimeout(timeoutId);
                console.error('❌ Unexpected database error:', error);
                reject(error);
            }
        });
    }

    /**
     * Get single query result with timeout protection
     */
    getQuery(sql, params = [], options = {}) {
        const timeout = options.timeout || 5000; // 5 second default timeout
        
        return new Promise((resolve, reject) => {
            // Create timeout handler
            const timeoutId = setTimeout(() => {
                console.error(`\u26a0\ufe0f GET query timeout after ${timeout}ms:`, sql.substring(0, 100));
                reject(new Error(`Database GET query timed out after ${timeout}ms`));
            }, timeout);
            
            try {
                this.db.get(sql, params, (err, row) => {
                    // Clear timeout since query completed
                    clearTimeout(timeoutId);
                    
                    if (err) {
                        console.error('\u274c Database GET error:', err.message);
                        console.error('   SQL:', sql.substring(0, 150));
                        reject(err);
                    } else {
                        resolve(row);
                    }
                });
            } catch (error) {
                // Clear timeout and handle unexpected errors
                clearTimeout(timeoutId);
                console.error('\u274c Unexpected GET database error:', error);
                reject(error);
            }
        });
    }

    /**
     * Get all query results with timeout protection
     */
    getAllQuery(sql, params = [], options = {}) {
        const timeout = options.timeout || 8000; // 8 second default timeout for potentially larger result sets
        
        return new Promise((resolve, reject) => {
            // Create timeout handler
            const timeoutId = setTimeout(() => {
                console.error(`\u26a0\ufe0f ALL query timeout after ${timeout}ms:`, sql.substring(0, 100));
                reject(new Error(`Database ALL query timed out after ${timeout}ms`));
            }, timeout);
            
            try {
                this.db.all(sql, params, (err, rows) => {
                    // Clear timeout since query completed
                    clearTimeout(timeoutId);
                    
                    if (err) {
                        console.error('\u274c Database ALL error:', err.message);
                        console.error('   SQL:', sql.substring(0, 150));
                        reject(err);
                    } else {
                        console.log(`Query returned ${rows ? rows.length : 0} rows`);
                        resolve(rows || []);
                    }
                });
            } catch (error) {
                // Clear timeout and handle unexpected errors
                clearTimeout(timeoutId);
                console.error('\u274c Unexpected ALL database error:', error);
                reject(error);
            }
        });
    }

    /**
     * Execute multiple queries in a transaction with timeout protection and better error handling
     */
    async executeTransaction(queries, options = {}) {
        const timeout = options.timeout || 30000; // 30 second default timeout for transactions
        
        return new Promise((resolve, reject) => {
            // Set transaction timeout
            const transactionTimeoutId = setTimeout(() => {
                console.error(`⚠️ Transaction timeout after ${timeout}ms - rolling back`);
                // Attempt to roll back the transaction on timeout
                try {
                    this.db.run('ROLLBACK', () => {
                        reject(new Error(`Transaction timed out after ${timeout}ms and was rolled back`));
                    });
                } catch (rollbackErr) {
                    reject(new Error(`Transaction timed out and rollback failed: ${rollbackErr.message}`));
                }
            }, timeout);
            
            try {
                this.db.serialize(() => {
                    // Begin transaction
                    this.db.run('BEGIN TRANSACTION', (beginErr) => {
                        if (beginErr) {
                            clearTimeout(transactionTimeoutId);
                            console.error('❌ Failed to begin transaction:', beginErr);
                            reject(beginErr);
                            return;
                        }
                        
                        // Track execution state
                        let completed = 0;
                        let errors = [];
                        let hasError = false;

                        // Function to execute next query in sequence
                        const executeNext = () => {
                            // All queries processed, finalize transaction
                            if (completed >= queries.length) {
                                if (hasError) {
                                    console.error(`❌ Rolling back transaction after ${errors.length} errors:`, 
                                        errors.map(e => e.message).join(', '));
                                    
                                    this.db.run('ROLLBACK', (rollbackErr) => {
                                        clearTimeout(transactionTimeoutId);
                                        if (rollbackErr) {
                                            console.error('❌❌ Rollback error:', rollbackErr);
                                        }
                                        reject(new Error(`Transaction failed with ${errors.length} errors`));
                                    });
                                } else {
                                    this.db.run('COMMIT', (commitErr) => {
                                        clearTimeout(transactionTimeoutId);
                                        if (commitErr) {
                                            console.error('❌ Commit error:', commitErr);
                                            reject(commitErr);
                                        } else {
                                            resolve({ success: true, processed: completed });
                                        }
                                    });
                                }
                                return;
                            }

                            // Execute current query
                            const { sql, params } = queries[completed];
                            try {
                                this.db.run(sql, params, function(err) {
                                    if (err && !hasError) {
                                        hasError = true;
                                        errors.push(err);
                                        console.error(`❌ Transaction query error at index ${completed}:`, err.message);
                                        console.error('   SQL:', sql.substring(0, 150));
                                    }
                                    
                                    completed++;
                                    
                                    // Use setImmediate to prevent UI blocking by yielding to event loop
                                    setImmediate(executeNext);
                                });
                            } catch (unexpectedErr) {
                                hasError = true;
                                errors.push(unexpectedErr);
                                console.error(`❌ Unexpected error in transaction at index ${completed}:`, unexpectedErr);
                                completed++;
                                setImmediate(executeNext);
                            }
                        };

                        // Start executing queries
                        executeNext();
                    });
                });
            } catch (error) {
                clearTimeout(transactionTimeoutId);
                console.error('❌ Critical transaction error:', error);
                reject(error);
            }
        });
    }

    // ========== PAYROLL AUDIT DATABASE METHODS ==========

    /**
     * Create new payroll processing session
     */
    async createPayrollSession(sessionId, pdfPath, totalPages = null) {
        const sql = `INSERT INTO payroll_sessions
                     (session_id, pdf_path, total_pages, processing_status)
                     VALUES (?, ?, ?, 'processing')`;
        return await this.runQuery(sql, [sessionId, pdfPath, totalPages]);
    }

    /**
     * Store employee data (bulk insert for performance)
     */
    async storeEmployees(sessionId, employees) {
        const queries = employees.map(emp => ({
            sql: `INSERT INTO employees
                  (session_id, employee_id, employee_name, department, section, job_title,
                   ssf_number, ghana_card_id, page_number, extraction_confidence)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            params: [
                sessionId, emp.employee_id, emp.employee_name, emp.department,
                emp.section, emp.job_title, emp.ssf_number, emp.ghana_card_id,
                emp.page_number, emp.extraction_confidence || 1.0
            ]
        }));

        return await this.executeTransaction(queries);
    }

    /**
     * Store extracted items (bulk insert for performance)
     */
    async storeExtractedItems(sessionId, items) {
        const queries = items.map(item => ({
            sql: `INSERT INTO extracted_items
                  (session_id, employee_id, section_name, item_label, item_value,
                   numeric_value, value_format, extraction_confidence, extraction_source)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            params: [
                sessionId, item.employee_id, item.section, item.label, item.value,
                this.parseNumericValue(item.value), item.format,
                item.confidence || 1.0, item.source || 'Perfect Section-Aware Extractor'
            ]
        }));

        return await this.executeTransaction(queries);
    }

    /**
     * Get payroll session statistics
     */
    async getPayrollSessionStats(sessionId) {
        const stats = {};

        stats.totalEmployees = await this.getQuery(
            'SELECT COUNT(*) as count FROM employees WHERE session_id = ?', [sessionId]
        );

        stats.totalItems = await this.getQuery(
            'SELECT COUNT(*) as count FROM extracted_items WHERE session_id = ?', [sessionId]
        );

        stats.sectionBreakdown = await this.getAllQuery(
            `SELECT section_name, COUNT(*) as count
             FROM extracted_items WHERE session_id = ?
             GROUP BY section_name ORDER BY count DESC`, [sessionId]
        );

        return {
            employees: stats.totalEmployees.count,
            extractedItems: stats.totalItems.count,
            sections: stats.sectionBreakdown
        };
    }

    /**
     * Run database migrations for existing tables
     */
    async runMigrations() {
        try {
            console.log('🔄 Running database migrations...');

            // Check if period_type column exists in employees table
            const employeesColumns = await this.getQuery("PRAGMA table_info(employees)");
            const hasPeriodTypeEmployees = employeesColumns.some(col => col.name === 'period_type');

            if (!hasPeriodTypeEmployees) {
                console.log('🔄 Adding period_type column to employees table...');
                await this.runQuery('ALTER TABLE employees ADD COLUMN period_type TEXT DEFAULT "current"');
                console.log('✅ Added period_type column to employees table');
            }

            // Check if period_type column exists in extracted_items table
            const itemsColumns = await this.getQuery("PRAGMA table_info(extracted_items)");
            const hasPeriodTypeItems = itemsColumns.some(col => col.name === 'period_type');

            if (!hasPeriodTypeItems) {
                console.log('🔄 Adding period_type column to extracted_items table...');
                await this.runQuery('ALTER TABLE extracted_items ADD COLUMN period_type TEXT DEFAULT "current"');
                console.log('✅ Added period_type column to extracted_items table');
            }

            // Silent migration completion to avoid encoding issues
        } catch (error) {
            console.error('❌ Database migration failed:', error);
            // Don't throw error to prevent app from crashing
        }
    }

    // ========== PHASED PROCESS DATABASE METHODS ==========

    /**
     * Create new phased process session
     */
    async createPhasedProcessSession(sessionId, currentPdfPath, previousPdfPath = null, options = {}) {
        const sql = `INSERT INTO phased_process_sessions
                     (session_id, current_pdf_path, previous_pdf_path, process_options, processing_status)
                     VALUES (?, ?, ?, ?, 'processing')`;
        return await this.runQuery(sql, [sessionId, currentPdfPath, previousPdfPath, JSON.stringify(options)]);
    }

    /**
     * Update phased process session status
     */
    async updatePhasedProcessSession(sessionId, updates) {
        const fields = [];
        const values = [];

        if (updates.completedPhases !== undefined) {
            fields.push('completed_phases = ?');
            values.push(updates.completedPhases);
        }
        if (updates.currentPhase !== undefined) {
            fields.push('current_phase = ?');
            values.push(updates.currentPhase);
        }
        if (updates.processingStatus !== undefined) {
            fields.push('processing_status = ?');
            values.push(updates.processingStatus);
        }
        if (updates.completedAt !== undefined) {
            fields.push('completed_at = ?');
            values.push(updates.completedAt);
        }

        if (fields.length === 0) return;

        values.push(sessionId);
        const sql = `UPDATE phased_process_sessions SET ${fields.join(', ')} WHERE session_id = ?`;
        return await this.runQuery(sql, values);
    }

    /**
     * Store phase result
     */
    async storePhaseResult(sessionId, phaseData) {
        const sql = `INSERT OR REPLACE INTO phase_results
                     (session_id, phase_name, phase_order, success, execution_time_seconds,
                      progress_percentage, error_message, data_summary, memory_usage_mb,
                      records_processed, completed_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;

        return await this.runQuery(sql, [
            sessionId,
            phaseData.phase_name,
            phaseData.phase_order,
            phaseData.success ? 1 : 0,
            phaseData.execution_time_seconds,
            phaseData.progress_percentage,
            phaseData.error_message,
            JSON.stringify(phaseData.data_summary || {}),
            phaseData.memory_usage_mb,
            phaseData.records_processed
        ]);
    }

    /**
     * Get phase results for session
     */
    async getPhaseResults(sessionId) {
        const sql = `SELECT * FROM phase_results WHERE session_id = ? ORDER BY phase_order`;
        return await this.getAllQuery(sql, [sessionId]);
    }

    /**
     * Store comparison results
     */
    async storeComparisonResults(sessionId, comparisonData) {
        const sql = `INSERT INTO comparison_results
                     (session_id, employee_id, employee_name, section_name, item_label,
                      previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        const insertPromises = comparisonData.map(change =>
            this.runQuery(sql, [
                sessionId,
                change.employee_id,
                change.employee_name,
                change.section_name,
                change.item_label,
                change.previous_value,
                change.current_value,
                change.change_type,
                change.priority,
                change.numeric_difference,
                change.percentage_change
            ])
        );

        return await Promise.all(insertPromises);
    }

    /**
     * Get comparison results for session
     */
    async getComparisonResults(sessionId) {
        const sql = `SELECT * FROM comparison_results WHERE session_id = ? ORDER BY priority DESC, employee_id`;
        return await this.getAllQuery(sql, [sessionId]);
    }

    /**
     * Store tracker results
     */
    async storeTrackerResults(sessionId, trackerData) {
        const sql = `INSERT INTO tracker_results
                     (session_id, employee_id, tracker_type, item_label, item_value,
                      numeric_value, classification, detection_confidence)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

        const insertPromises = trackerData.map(item =>
            this.runQuery(sql, [
                sessionId,
                item.employee_id,
                item.tracker_type,
                item.item_label,
                item.item_value,
                item.numeric_value,
                item.classification,
                item.detection_confidence
            ])
        );

        return await Promise.all(insertPromises);
    }

    /**
     * Get session summary with all phase results
     */
    async getPhasedProcessSummary(sessionId) {
        const sessionSql = `SELECT * FROM phased_process_sessions WHERE session_id = ?`;
        const session = await this.getQuery(sessionSql, [sessionId]);

        if (!session) return null;

        const phaseResults = await this.getPhaseResults(sessionId);
        const comparisonResults = await this.getComparisonResults(sessionId);
        const payrollStats = await this.getPayrollSessionStats(sessionId);

        return {
            session,
            phaseResults,
            comparisonResults,
            payrollStats
        };
    }

    // ========== DICTIONARY MANAGER DATABASE METHODS ==========

    /**
     * Load dictionary from database
     */
    async loadDictionary() {
        const sections = await this.getAllQuery(
            'SELECT * FROM dictionary_sections ORDER BY id'
        );

        const dictionary = {};

        for (const section of sections) {
            const items = await this.getAllQuery(
                'SELECT * FROM dictionary_items WHERE section_id = ?', [section.id]
            );

            dictionary[section.section_name] = {
                items: {}
            };

            for (const item of items) {
                const itemData = {
                    format: item.format_type || 'text',
                    value_format: item.value_format || 'text',
                    include_in_report: item.include_in_report === 1,
                    standard_key: item.standard_key,
                    standardized_name: item.standard_key || item.item_name,
                    is_fixed: item.is_fixed === 1,
                    variations: item.value_format ? item.value_format.split(',').filter(v => v.trim()) : [],
                    validation_rules: {}
                };

                // Extract loan classification if available
                if (item.loan_classification) {
                    itemData.loan_classification = item.loan_classification;
                    itemData.validation_rules.loan_classification = item.loan_classification;
                }

                dictionary[section.section_name].items[item.item_name] = itemData;
            }
        }

        return dictionary;
    }

    /**
     * Save dictionary to database
     */
    async saveDictionary(dictionary) {
        const queries = [];

        // Clear existing data
        queries.push({ sql: 'DELETE FROM dictionary_items', params: [] });
        queries.push({ sql: 'DELETE FROM dictionary_sections', params: [] });

        for (const [sectionName, sectionData] of Object.entries(dictionary)) {
            // Insert section
            queries.push({
                sql: 'INSERT INTO dictionary_sections (section_name, section_order, is_active) VALUES (?, ?, 1)',
                params: [sectionName, Object.entries(dictionary).indexOf([sectionName, sectionData])]
            });

            // Insert items for this section
            if (sectionData.items) {
                for (const [itemName, itemData] of Object.entries(sectionData.items)) {
                    queries.push({
                        sql: `INSERT OR REPLACE INTO dictionary_items
                              (section_id, item_name, standard_key, format_type, value_format,
                               include_in_report, is_fixed, validation_rules)
                              VALUES ((SELECT id FROM dictionary_sections WHERE section_name = ?),
                                      ?, ?, ?, ?, ?, ?, ?)`,
                        params: [
                            sectionName, itemName, itemData.standardized_name || itemName,
                            itemData.format || 'text', itemData.value_format || 'text',
                            itemData.include_in_report ? 1 : 0, itemData.is_fixed ? 1 : 0,
                            JSON.stringify(itemData.validation_rules || {})
                        ]
                    });
                }
            }
        }

        return await this.executeTransaction(queries);
    }

    // ========== AUTO LEARNING DATABASE METHODS ==========

    /**
     * Start auto learning session
     */
    async startAutoLearningSession(sessionName = null) {
        const sessionId = `auto_learning_${Date.now()}`;
        const sql = `INSERT INTO auto_learning_sessions (session_id, session_name) VALUES (?, ?)`;
        await this.runQuery(sql, [sessionId, sessionName]);
        return sessionId;
    }

    /**
     * Add pending item to auto learning
     */
    async addPendingItem(sessionId, itemData) {
        const sql = `INSERT INTO pending_items
                     (session_id, item_label, suggested_section, suggested_standard_name,
                      confidence_score, occurrence_count, first_seen_in)
                     VALUES (?, ?, ?, ?, ?, ?, ?)`;

        return await this.runQuery(sql, [
            sessionId, itemData.label, itemData.suggested_section,
            itemData.suggested_standard_name, itemData.confidence,
            itemData.occurrence_count || 1, itemData.first_seen_in
        ]);
    }

    /**
     * Get pending items for approval
     */
    async getPendingItems(sessionId = null) {
        let sql = 'SELECT * FROM pending_items WHERE status = "pending_approval"';
        let params = [];

        if (sessionId) {
            sql += ' AND session_id = ?';
            params.push(sessionId);
        }

        sql += ' ORDER BY occurrence_count DESC, created_at ASC';

        return await this.getAllQuery(sql, params);
    }

    /**
     * Approve pending item
     */
    async approvePendingItem(itemId, standardizedName, targetSection) {
        const sql = `UPDATE pending_items
                     SET status = 'approved',
                         suggested_standard_name = ?,
                         suggested_section = ?,
                         processed_at = CURRENT_TIMESTAMP
                     WHERE id = ?`;

        return await this.runQuery(sql, [standardizedName, targetSection, itemId]);
    }

    /**
     * Reject pending item
     */
    async rejectPendingItem(itemId, reason = null) {
        const sql = `UPDATE pending_items
                     SET status = 'rejected',
                         rejection_reason = ?,
                         processed_at = CURRENT_TIMESTAMP
                     WHERE id = ?`;

        return await this.runQuery(sql, [reason, itemId]);
    }

    // ========== REPORT MANAGER DATABASE METHODS ==========

    /**
     * Save report to database
     */
    async saveReport(reportData) {
        const sql = `INSERT INTO reports
                     (report_id, report_type, report_category, title, description,
                      file_paths, metadata, file_size)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

        return await this.runQuery(sql, [
            reportData.report_id, reportData.report_type, reportData.report_category,
            reportData.title, reportData.description,
            JSON.stringify(reportData.file_paths), JSON.stringify(reportData.metadata),
            reportData.file_size
        ]);
    }

    /**
     * Get all reports organized by category
     */
    async getAllReports() {
        const sql = `SELECT * FROM reports WHERE is_archived = 0
                     ORDER BY report_category, created_at DESC`;

        const reports = await this.getAllQuery(sql);

        // Organize by category
        const organized = {};
        for (const report of reports) {
            if (!organized[report.report_category]) {
                organized[report.report_category] = [];
            }

            // Parse JSON fields
            report.file_paths = JSON.parse(report.file_paths || '{}');
            report.metadata = JSON.parse(report.metadata || '{}');

            organized[report.report_category].push(report);
        }

        return organized;
    }

    /**
     * Delete report
     */
    async deleteReport(reportId) {
        const sql = 'UPDATE reports SET is_archived = 1 WHERE report_id = ?';
        return await this.runQuery(sql, [reportId]);
    }

    // ========== PDF SORTER DATABASE METHODS ==========

    /**
     * Create PDF sorting session
     */
    async createPdfSortingSession(sessionId, pdfPath, sortCriteria) {
        const sql = `INSERT INTO pdf_sorting_sessions
                     (session_id, original_pdf_path, sort_criteria, processing_status)
                     VALUES (?, ?, ?, 'processing')`;

        return await this.runQuery(sql, [sessionId, pdfPath, JSON.stringify(sortCriteria)]);
    }

    /**
     * Store sorted payslips
     */
    async storeSortedPayslips(sessionId, payslips) {
        const queries = payslips.map((payslip, index) => ({
            sql: `INSERT INTO sorted_payslips
                  (session_id, employee_id, employee_name, department, section,
                   original_page, sorted_position, extraction_confidence)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            params: [
                sessionId, payslip.employee_id, payslip.employee_name,
                payslip.department, payslip.section, payslip.original_page,
                index + 1, payslip.extraction_confidence || 1.0
            ]
        }));

        return await this.executeTransaction(queries);
    }

    /**
     * Complete PDF sorting session
     */
    async completePdfSortingSession(sessionId, sortedPdfPath, totalPayslips) {
        const sql = `UPDATE pdf_sorting_sessions
                     SET sorted_pdf_path = ?, total_payslips = ?,
                         processing_status = 'completed', completed_at = CURRENT_TIMESTAMP
                     WHERE session_id = ?`;

        return await this.runQuery(sql, [sortedPdfPath, totalPayslips, sessionId]);
    }

    // ========== DATA BUILDER DATABASE METHODS ==========

    /**
     * Create Data Builder session
     */
    async createDataBuilderSession(sessionId, sourceFiles, processingOptions) {
        const sql = `INSERT INTO data_builder_sessions
                     (session_id, source_files, processing_options, processing_status)
                     VALUES (?, ?, ?, 'processing')`;

        return await this.runQuery(sql, [
            sessionId, JSON.stringify(sourceFiles), JSON.stringify(processingOptions)
        ]);
    }

    /**
     * Store column definitions
     */
    async storeColumnDefinitions(sessionId, columns) {
        const queries = columns.map(col => ({
            sql: `INSERT INTO column_definitions
                  (session_id, column_name, original_label, section_name, format_type,
                   value_type, include_in_report, occurrence_count, first_seen_employee)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            params: [
                sessionId, col.column_name, col.original_label, col.section_name,
                col.format_type, col.value_type, col.include_in_report ? 1 : 0,
                col.occurrence_count || 1, col.first_seen_employee
            ]
        }));

        return await this.executeTransaction(queries);
    }

    /**
     * Store data deficits
     */
    async storeDataDeficits(sessionId, deficits) {
        const queries = deficits.map(deficit => ({
            sql: `INSERT INTO data_deficits
                  (session_id, employee_id, missing_field, expected_section, severity)
                  VALUES (?, ?, ?, ?, ?)`,
            params: [
                sessionId, deficit.employee_id, deficit.missing_field,
                deficit.expected_section, deficit.severity
            ]
        }));

        return await this.executeTransaction(queries);
    }

    /**
     * Complete Data Builder session
     */
    async completeDataBuilderSession(sessionId, outputFilePath, totalEmployees, totalColumns, dataDeficits) {
        const sql = `UPDATE data_builder_sessions
                     SET output_file_path = ?, total_employees = ?, total_columns = ?,
                         data_deficits = ?, processing_status = 'completed',
                         completed_at = CURRENT_TIMESTAMP
                     WHERE session_id = ?`;

        return await this.runQuery(sql, [
            outputFilePath, totalEmployees, totalColumns, dataDeficits, sessionId
        ]);
    }

    // ========== PERFORMANCE MONITORING METHODS ==========

    /**
     * Record performance metrics
     */
    async recordPerformanceMetric(moduleName, operationType, executionTime, memoryUsage, recordsProcessed, successRate) {
        const sql = `INSERT INTO performance_metrics
                     (module_name, operation_type, execution_time_ms, memory_usage_mb,
                      records_processed, success_rate)
                     VALUES (?, ?, ?, ?, ?, ?)`;

        return await this.runQuery(sql, [
            moduleName, operationType, executionTime, memoryUsage, recordsProcessed, successRate
        ]);
    }

    /**
     * Get performance statistics
     */
    async getPerformanceStats(moduleName = null, hours = 24) {
        let sql = `SELECT module_name, operation_type,
                          AVG(execution_time_ms) as avg_time,
                          AVG(memory_usage_mb) as avg_memory,
                          SUM(records_processed) as total_records,
                          AVG(success_rate) as avg_success_rate,
                          COUNT(*) as operation_count
                   FROM performance_metrics
                   WHERE created_at > datetime('now', '-${hours} hours')`;

        let params = [];
        if (moduleName) {
            sql += ' AND module_name = ?';
            params.push(moduleName);
        }

        sql += ' GROUP BY module_name, operation_type ORDER BY avg_time DESC';

        return await this.getAllQuery(sql, params);
    }

    // ========== UTILITY METHODS ==========

    /**
     * Parse numeric value from string
     */
    parseNumericValue(value) {
        if (typeof value === 'number') return value;
        if (typeof value !== 'string') return null;

        // Remove common formatting
        const cleaned = value.replace(/[,\s]/g, '').replace(/[^\d.-]/g, '');
        const parsed = parseFloat(cleaned);

        return isNaN(parsed) ? null : parsed;
    }

    /**
     * Get comprehensive system statistics
     */
    async getSystemStatistics() {
        const stats = {};

        // Payroll Audit stats
        stats.payrollAudit = {
            totalSessions: await this.getQuery('SELECT COUNT(*) as count FROM payroll_sessions'),
            totalEmployees: await this.getQuery('SELECT COUNT(*) as count FROM employees'),
            totalExtractedItems: await this.getQuery('SELECT COUNT(*) as count FROM extracted_items')
        };

        // Dictionary stats
        stats.dictionary = {
            totalSections: await this.getQuery('SELECT COUNT(*) as count FROM dictionary_sections'),
            totalItems: await this.getQuery('SELECT COUNT(*) as count FROM dictionary_items'),
            totalLoanTypes: await this.getQuery('SELECT COUNT(*) as count FROM dictionary_items WHERE loan_classification IS NOT NULL')
        };

        // Auto Learning stats
        stats.autoLearning = {
            activeSessions: await this.getQuery('SELECT COUNT(*) as count FROM auto_learning_sessions'),
            pendingItems: await this.getQuery('SELECT COUNT(*) as count FROM pending_items WHERE status = "pending_approval"'),
            approvedItems: await this.getQuery('SELECT COUNT(*) as count FROM pending_items WHERE status = "approved"')
        };

        // Report Manager stats
        stats.reports = {
            totalReports: await this.getQuery('SELECT COUNT(*) as count FROM reports WHERE is_archived = 0'),
            reportsByCategory: await this.getAllQuery('SELECT report_category, COUNT(*) as count FROM reports WHERE is_archived = 0 GROUP BY report_category')
        };

        // PDF Sorter stats
        stats.pdfSorter = {
            totalSessions: await this.getQuery('SELECT COUNT(*) as count FROM pdf_sorting_sessions'),
            totalSortedPayslips: await this.getQuery('SELECT COUNT(*) as count FROM sorted_payslips')
        };

        // Data Builder stats
        stats.dataBuilder = {
            totalSessions: await this.getQuery('SELECT COUNT(*) as count FROM data_builder_sessions'),
            totalColumns: await this.getQuery('SELECT COUNT(*) as count FROM column_definitions'),
            totalDeficits: await this.getQuery('SELECT COUNT(*) as count FROM data_deficits')
        };

        return stats;
    }

    /**
     * Clean old data (for maintenance)
     */
    async cleanOldData(daysOld = 30) {
        const queries = [
            { sql: `DELETE FROM performance_metrics WHERE created_at < datetime('now', '-${daysOld} days')`, params: [] },
            { sql: `DELETE FROM payroll_sessions WHERE created_at < datetime('now', '-${daysOld} days') AND processing_status = 'completed'`, params: [] },
            { sql: `UPDATE reports SET is_archived = 1 WHERE created_at < datetime('now', '-${daysOld} days')`, params: [] }
        ];

        return await this.executeTransaction(queries);
    }

    /**
     * Check if a table exists in the database
     */
    async tableExists(tableName) {
        return new Promise((resolve, reject) => {
            const sql = `SELECT name FROM sqlite_master WHERE type='table' AND name=?`;
            this.db.get(sql, [tableName], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(!!row);
                }
            });
        });
    }

    /**
     * Clean extracted data (for payroll audit cleanup) - SAFE VERSION
     * Note: Tracker data is in Bank Adviser module, not cleaned here
     * This method uses simple queries instead of complex transactions to avoid FATAL ERRORS
     */
    async cleanExtractedData(options = {}) {
        // Define potential tables to clean (only payroll audit related)
        const potentialTables = [
            'extracted_data',
            'audit_sessions',
            'comparison_results',
            'pre_reporting_data'
        ];

        // Check which tables actually exist
        const existingTables = [];
        for (const tableName of potentialTables) {
            try {
                const exists = await this.tableExists(tableName);
                if (exists) {
                    existingTables.push(tableName);
                    console.log(`✅ Table ${tableName} exists - will be cleaned`);
                } else {
                    console.log(`⚠️ Table ${tableName} does not exist - skipping`);
                }
            } catch (error) {
                console.error(`❌ Error checking table ${tableName}:`, error.message);
            }
        }

        if (existingTables.length === 0) {
            console.log('ℹ️ No payroll audit tables found to clean');
            return { success: true, message: 'No tables found to clean', tablesChecked: potentialTables };
        }

        console.log(`🧹 Cleaning ${existingTables.length} tables: ${existingTables.join(', ')}`);

        // CRITICAL FIX: Use simple individual queries instead of complex transactions
        // This prevents the FATAL ERROR caused by transaction timeout/callback issues
        let cleanedCount = 0;
        const errors = [];

        for (const tableName of existingTables) {
            try {
                let sql;
                let params = [];

                if (options.sessionId) {
                    // Clean specific session
                    if (tableName === 'extracted_data' || tableName === 'audit_sessions') {
                        sql = `DELETE FROM ${tableName} WHERE session_id = ?`;
                        params = [options.sessionId];
                    } else {
                        continue; // Skip tables that don't have session_id
                    }
                } else if (options.daysOld) {
                    // Clean old data
                    sql = `DELETE FROM ${tableName} WHERE created_at < datetime('now', '-${options.daysOld} days')`;
                } else {
                    // Clean all data
                    sql = `DELETE FROM ${tableName}`;
                }

                // Execute simple query without transaction complexity
                await this.runQuery(sql, params);
                console.log(`✅ Cleaned table: ${tableName}`);
                cleanedCount++;

            } catch (error) {
                console.error(`❌ Error cleaning table ${tableName}:`, error.message);
                errors.push({ table: tableName, error: error.message });
            }
        }

        const result = {
            success: cleanedCount > 0,
            message: `Cleaned ${cleanedCount}/${existingTables.length} tables`,
            tablesChecked: potentialTables,
            tablesCleaned: existingTables.slice(0, cleanedCount),
            queriesExecuted: cleanedCount,
            errors: errors
        };

        if (errors.length > 0) {
            console.warn(`⚠️ Some tables had errors: ${errors.length}/${existingTables.length}`);
        }

        return result;
    }

    /**
     * Clean up duplicate or problematic audit sessions
     */
    async cleanDuplicateAuditSessions() {
        try {
            // Check if audit_sessions table exists
            const auditSessionsExists = await this.tableExists('audit_sessions');
            if (!auditSessionsExists) {
                console.log('⚠️ audit_sessions table does not exist - skipping cleanup');
                return { success: true, message: 'No audit_sessions table to clean' };
            }

            // Find and remove duplicate sessions (keep the most recent one for each session_id pattern)
            const duplicateCleanupQuery = `
                DELETE FROM audit_sessions
                WHERE id NOT IN (
                    SELECT MAX(id)
                    FROM audit_sessions
                    GROUP BY session_id
                )
            `;

            // Also clean up any sessions with invalid or problematic data
            const invalidCleanupQuery = `
                DELETE FROM audit_sessions
                WHERE session_id IS NULL
                   OR session_id = ''
                   OR current_pdf_path IS NULL
                   OR previous_pdf_path IS NULL
            `;

            const queries = [
                { sql: duplicateCleanupQuery, params: [] },
                { sql: invalidCleanupQuery, params: [] }
            ];

            console.log('🧹 Cleaning up duplicate and invalid audit sessions...');
            const result = await this.executeTransaction(queries);

            console.log('✅ Audit sessions cleanup completed');
            return { success: true, result: result };

        } catch (error) {
            console.error('❌ Error cleaning duplicate audit sessions:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Close database connection
     */
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err);
                    } else {
                        console.log('🗄️ Unified database connection closed');
                    }
                    this.isConnected = false;
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = UnifiedDatabase;
