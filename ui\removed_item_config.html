<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Removed Item Filtering Configuration</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.2em;
        }
        
        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .config-section h3 {
            color: #34495e;
            margin-top: 0;
            font-size: 1.4em;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 10px;
        }
        
        .toggle-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            margin-right: 15px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 30px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #27ae60;
        }
        
        input:checked + .slider:before {
            transform: translateX(30px);
        }
        
        .rule-list {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .rule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .rule-item:last-child {
            border-bottom: none;
        }
        
        .rule-name {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .rule-action {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }
        
        .action-always {
            background-color: #e74c3c;
            color: white;
        }
        
        .action-never {
            background-color: #27ae60;
            color: white;
        }
        
        .action-analyze {
            background-color: #f39c12;
            color: white;
        }
        
        .add-rule-form {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 4px;
        }
        
        .add-rule-form input, .add-rule-form select {
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .add-rule-form input {
            flex: 1;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .threshold-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .threshold-item {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .threshold-item label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .threshold-item input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        
        .threshold-value {
            text-align: center;
            font-weight: bold;
            color: #27ae60;
        }
        
        .save-button {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e0e0e0;
        }
        
        .btn-save {
            background-color: #27ae60;
            color: white;
            padding: 12px 30px;
            font-size: 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-save:hover {
            background-color: #229954;
        }
        
        .stats-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            text-align: center;
        }
        
        .stat-item h4 {
            margin: 0;
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .stat-item p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ Removed Item Filtering Configuration</h1>
            <p>Configure intelligent filtering for REMOVED items to reduce noise in pre-reporting</p>
        </div>
        
        <div class="stats-panel">
            <div class="stats-grid">
                <div class="stat-item">
                    <h4 id="totalRules">0</h4>
                    <p>Total Rules</p>
                </div>
                <div class="stat-item">
                    <h4 id="alwaysReport">0</h4>
                    <p>Always Report</p>
                </div>
                <div class="stat-item">
                    <h4 id="neverReport">0</h4>
                    <p>Never Report</p>
                </div>
                <div class="stat-item">
                    <h4 id="patternRules">0</h4>
                    <p>Pattern Rules</p>
                </div>
            </div>
        </div>
        
        <div class="config-section">
            <h3>🎛️ Master Control</h3>
            <div class="toggle-container">
                <label class="toggle-switch">
                    <input type="checkbox" id="enableFiltering" checked>
                    <span class="slider"></span>
                </label>
                <label for="enableFiltering">
                    <strong>Enable Smart REMOVED Item Filtering</strong><br>
                    <small>When enabled, routine REMOVED items will be filtered out automatically</small>
                </label>
            </div>
        </div>
        
        <div class="config-section">
            <h3>📋 Item-Specific Rules</h3>
            <p>Configure specific actions for individual items. These rules have the highest priority.</p>
            <div class="rule-list" id="itemRulesList">
                <!-- Rules will be populated by JavaScript -->
            </div>
            <div class="add-rule-form">
                <input type="text" id="newItemName" placeholder="Item name (e.g., BASIC SALARY)">
                <select id="newItemAction">
                    <option value="ALWAYS_REPORT">Always Report</option>
                    <option value="NEVER_REPORT">Never Report</option>
                    <option value="ANALYZE">Analyze</option>
                </select>
                <button class="btn btn-primary" onclick="addItemRule()">Add Rule</button>
            </div>
        </div>
        
        <div class="config-section">
            <h3>🎯 Pattern-Based Rules</h3>
            <p>Configure pattern-based rules using wildcards (*). These apply to multiple items at once.</p>
            <div class="rule-list" id="patternRulesList">
                <!-- Pattern rules will be populated by JavaScript -->
            </div>
            <div class="add-rule-form">
                <input type="text" id="newPatternName" placeholder="Pattern (e.g., *OVERTIME*, SPECIAL*)">
                <select id="newPatternAction">
                    <option value="ALWAYS_REPORT">Always Report</option>
                    <option value="NEVER_REPORT">Never Report</option>
                    <option value="ANALYZE">Analyze</option>
                </select>
                <button class="btn btn-primary" onclick="addPatternRule()">Add Pattern</button>
            </div>
        </div>
        
        <div class="config-section">
            <h3>🧠 Smart Analysis Thresholds</h3>
            <p>Configure thresholds for intelligent analysis of items not covered by specific rules.</p>
            <div class="threshold-controls">
                <div class="threshold-item">
                    <label>Frequency Threshold</label>
                    <input type="range" id="frequencyThreshold" min="0.1" max="0.9" step="0.1" value="0.3">
                    <div class="threshold-value" id="frequencyValue">30%</div>
                    <small>Items appearing less frequently are considered routine</small>
                </div>
                <div class="threshold-item">
                    <label>Employee Impact Threshold</label>
                    <input type="range" id="employeeThreshold" min="0.05" max="0.5" step="0.05" value="0.1">
                    <div class="threshold-value" id="employeeValue">10%</div>
                    <small>Items affecting fewer employees are considered routine</small>
                </div>
                <div class="threshold-item">
                    <label>Amount Threshold</label>
                    <input type="range" id="amountThreshold" min="100" max="5000" step="100" value="1000">
                    <div class="threshold-value" id="amountValue">$1,000</div>
                    <small>Smaller amounts are considered less significant</small>
                </div>
            </div>
        </div>
        
        <div class="save-button">
            <button class="btn-save" onclick="saveConfiguration()">💾 Save Configuration</button>
        </div>
    </div>
    
    <script>
        // Configuration data
        let config = {
            enable_smart_filtering: true,
            item_rules: {},
            pattern_rules: {},
            smart_thresholds: {
                frequency_threshold: 0.3,
                employee_impact_threshold: 0.1,
                amount_threshold: 1000
            }
        };
        
        // Load configuration on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadConfiguration();
            setupEventListeners();
        });
        
        function loadConfiguration() {
            // In a real implementation, this would load from the backend
            // For now, we'll use default configuration
            config = {
                enable_smart_filtering: true,
                item_rules: {
                    'BASIC SALARY': 'ALWAYS_REPORT',
                    'GROSS SALARY': 'ALWAYS_REPORT',
                    'NET PAY': 'ALWAYS_REPORT',
                    'EMPLOYEE NO.': 'ALWAYS_REPORT',
                    'OVERTIME': 'NEVER_REPORT',
                    'BONUS': 'NEVER_REPORT',
                    'TRAVEL ALLOWANCE': 'NEVER_REPORT'
                },
                pattern_rules: {
                    '*OVERTIME*': 'NEVER_REPORT',
                    '*BONUS*': 'NEVER_REPORT',
                    '*ALLOWANCE': 'NEVER_REPORT',
                    'SPECIAL*': 'NEVER_REPORT',
                    'TEMP*': 'NEVER_REPORT'
                },
                smart_thresholds: {
                    frequency_threshold: 0.3,
                    employee_impact_threshold: 0.1,
                    amount_threshold: 1000
                }
            };
            
            updateUI();
        }
        
        function updateUI() {
            // Update master toggle
            document.getElementById('enableFiltering').checked = config.enable_smart_filtering;
            
            // Update item rules
            updateItemRulesList();
            
            // Update pattern rules
            updatePatternRulesList();
            
            // Update thresholds
            updateThresholds();
            
            // Update stats
            updateStats();
        }
        
        function updateItemRulesList() {
            const container = document.getElementById('itemRulesList');
            container.innerHTML = '';
            
            for (const [item, action] of Object.entries(config.item_rules)) {
                const div = document.createElement('div');
                div.className = 'rule-item';
                div.innerHTML = `
                    <span class="rule-name">${item}</span>
                    <div>
                        <span class="rule-action action-${action.toLowerCase().replace('_', '-')}">${action.replace('_', ' ')}</span>
                        <button class="btn btn-danger" style="margin-left: 10px; padding: 4px 8px; font-size: 12px;" onclick="removeItemRule('${item}')">Remove</button>
                    </div>
                `;
                container.appendChild(div);
            }
        }
        
        function updatePatternRulesList() {
            const container = document.getElementById('patternRulesList');
            container.innerHTML = '';
            
            for (const [pattern, action] of Object.entries(config.pattern_rules)) {
                const div = document.createElement('div');
                div.className = 'rule-item';
                div.innerHTML = `
                    <span class="rule-name">${pattern}</span>
                    <div>
                        <span class="rule-action action-${action.toLowerCase().replace('_', '-')}">${action.replace('_', ' ')}</span>
                        <button class="btn btn-danger" style="margin-left: 10px; padding: 4px 8px; font-size: 12px;" onclick="removePatternRule('${pattern}')">Remove</button>
                    </div>
                `;
                container.appendChild(div);
            }
        }
        
        function updateThresholds() {
            const freq = config.smart_thresholds.frequency_threshold;
            const emp = config.smart_thresholds.employee_impact_threshold;
            const amt = config.smart_thresholds.amount_threshold;
            
            document.getElementById('frequencyThreshold').value = freq;
            document.getElementById('employeeThreshold').value = emp;
            document.getElementById('amountThreshold').value = amt;
            
            document.getElementById('frequencyValue').textContent = Math.round(freq * 100) + '%';
            document.getElementById('employeeValue').textContent = Math.round(emp * 100) + '%';
            document.getElementById('amountValue').textContent = '$' + amt.toLocaleString();
        }
        
        function updateStats() {
            const totalRules = Object.keys(config.item_rules).length + Object.keys(config.pattern_rules).length;
            const alwaysReport = Object.values(config.item_rules).filter(a => a === 'ALWAYS_REPORT').length + 
                               Object.values(config.pattern_rules).filter(a => a === 'ALWAYS_REPORT').length;
            const neverReport = Object.values(config.item_rules).filter(a => a === 'NEVER_REPORT').length + 
                              Object.values(config.pattern_rules).filter(a => a === 'NEVER_REPORT').length;
            const patternRules = Object.keys(config.pattern_rules).length;
            
            document.getElementById('totalRules').textContent = totalRules;
            document.getElementById('alwaysReport').textContent = alwaysReport;
            document.getElementById('neverReport').textContent = neverReport;
            document.getElementById('patternRules').textContent = patternRules;
        }
        
        function setupEventListeners() {
            // Master toggle
            document.getElementById('enableFiltering').addEventListener('change', function() {
                config.enable_smart_filtering = this.checked;
            });
            
            // Threshold sliders
            document.getElementById('frequencyThreshold').addEventListener('input', function() {
                config.smart_thresholds.frequency_threshold = parseFloat(this.value);
                document.getElementById('frequencyValue').textContent = Math.round(this.value * 100) + '%';
            });
            
            document.getElementById('employeeThreshold').addEventListener('input', function() {
                config.smart_thresholds.employee_impact_threshold = parseFloat(this.value);
                document.getElementById('employeeValue').textContent = Math.round(this.value * 100) + '%';
            });
            
            document.getElementById('amountThreshold').addEventListener('input', function() {
                config.smart_thresholds.amount_threshold = parseInt(this.value);
                document.getElementById('amountValue').textContent = '$' + this.value.toLocaleString();
            });
        }
        
        function addItemRule() {
            const name = document.getElementById('newItemName').value.trim().toUpperCase();
            const action = document.getElementById('newItemAction').value;
            
            if (name) {
                config.item_rules[name] = action;
                document.getElementById('newItemName').value = '';
                updateItemRulesList();
                updateStats();
            }
        }
        
        function addPatternRule() {
            const pattern = document.getElementById('newPatternName').value.trim().toUpperCase();
            const action = document.getElementById('newPatternAction').value;
            
            if (pattern) {
                config.pattern_rules[pattern] = action;
                document.getElementById('newPatternName').value = '';
                updatePatternRulesList();
                updateStats();
            }
        }
        
        function removeItemRule(item) {
            delete config.item_rules[item];
            updateItemRulesList();
            updateStats();
        }
        
        function removePatternRule(pattern) {
            delete config.pattern_rules[pattern];
            updatePatternRulesList();
            updateStats();
        }
        
        function saveConfiguration() {
            // In a real implementation, this would save to the backend
            console.log('Saving configuration:', config);
            alert('Configuration saved successfully!\\n\\nThis will take effect on the next payroll audit run.');
        }
    </script>
</body>
</html>
