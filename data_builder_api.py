#!/usr/bin/env python3
"""
Data Builder API
Provides API interface for data builder operations
"""

import sys
import json
import os
from typing import Dict, Any

def initialize() -> Dict[str, Any]:
    """Initialize the data builder"""
    try:
        # Add core directory to path
        core_path = os.path.join(os.path.dirname(__file__), 'core')
        if core_path not in sys.path:
            sys.path.append(core_path)
        
        # Import data builder components
        from core.data_builder import DataBuilder
        
        # Initialize data builder
        builder = DataBuilder()
        
        return {
            'success': True,
            'message': 'Data builder initialized successfully',
            'status': 'ready'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to initialize data builder'
        }

def get_status() -> Dict[str, Any]:
    """Get data builder status"""
    return {
        'success': True,
        'status': 'ready',
        'message': 'Data builder is operational'
    }

def process_data(data: Any) -> Dict[str, Any]:
    """Process data through data builder"""
    try:
        # Add core directory to path
        core_path = os.path.join(os.path.dirname(__file__), 'core')
        if core_path not in sys.path:
            sys.path.append(core_path)
        
        # Import and use data builder
        from core.data_builder import DataBuilder
        
        builder = DataBuilder()
        result = builder.process(data)
        
        return {
            'success': True,
            'result': result,
            'message': 'Data processed successfully'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to process data'
        }

def main():
    """Main entry point for command line usage"""
    if len(sys.argv) < 2:
        print(json.dumps({
            'success': False,
            'error': 'No command provided',
            'usage': 'python data_builder_api.py <command> [args...]'
        }))
        return
    
    command = sys.argv[1]
    args = sys.argv[2:] if len(sys.argv) > 2 else []
    
    try:
        if command == 'initialize':
            result = initialize()
        elif command == 'status':
            result = get_status()
        elif command == 'process':
            # Expect data as JSON string in args
            data = json.loads(args[0]) if args else {}
            result = process_data(data)
        else:
            result = {
                'success': False,
                'error': f'Unknown command: {command}',
                'available_commands': ['initialize', 'status', 'process']
            }
        
        print(json.dumps(result))
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'command': command,
            'args': args
        }
        print(json.dumps(error_result))

if __name__ == '__main__':
    main()
