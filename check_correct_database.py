#!/usr/bin/env python3
"""
Check the correct database that PhasedProcessManager uses
"""

import sqlite3
import os

def check_correct_database():
    # Check the correct database path
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    print(f'Checking database: {db_path}')
    print(f'Database exists: {os.path.exists(db_path)}')

    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print()
        print('=== CHECKING CORRECT DATABASE ===')
        
        # Check tables
        cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
        tables = [row[0] for row in cursor.fetchall()]
        print(f'Tables: {tables}')
        
        if 'pre_reporting_results' in tables:
            cursor.execute('PRAGMA table_info(pre_reporting_results)')
            columns = cursor.fetchall()
            print()
            print('pre_reporting_results columns:')
            for col in columns:
                print(f'  - {col[1]} ({col[2]})')
        else:
            print('pre_reporting_results table does not exist!')
        
        if 'audit_sessions' in tables:
            cursor.execute('SELECT COUNT(*) FROM audit_sessions')
            count = cursor.fetchone()[0]
            print(f'audit_sessions: {count} records')
        else:
            print('audit_sessions table does not exist!')
        
        conn.close()
    else:
        print('Database does not exist!')

if __name__ == "__main__":
    check_correct_database()
