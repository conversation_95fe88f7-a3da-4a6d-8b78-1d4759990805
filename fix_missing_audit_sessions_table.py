#!/usr/bin/env python3
"""
Fix the missing audit_sessions table in the existing database
"""

import sqlite3
import os

def fix_missing_audit_sessions_table():
    """Add the missing audit_sessions table to the existing database"""
    
    print("=== FIXING MISSING AUDIT_SESSIONS TABLE ===")
    print()
    
    try:
        db_path = 'payroll_audit.db'
        if not os.path.exists(db_path):
            print("❌ Database file doesn't exist")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("1. Checking if audit_sessions table exists:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_sessions'")
        exists = cursor.fetchone()
        
        if exists:
            print("   ✅ audit_sessions table already exists")
            return True
        
        print("   ❌ audit_sessions table missing - creating it...")
        
        print()
        print("2. Creating audit_sessions table:")
        
        # Create the audit_sessions table with the exact schema the code expects
        create_table_sql = """
            CREATE TABLE IF NOT EXISTS audit_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                current_pdf_path TEXT,
                previous_pdf_path TEXT,
                current_month TEXT,
                current_year TEXT,
                previous_month TEXT,
                previous_year TEXT,
                status TEXT DEFAULT 'in_progress',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                stopped_at DATETIME,
                total_employees INTEGER DEFAULT 0,
                total_changes INTEGER DEFAULT 0
            )
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        
        print("   ✅ audit_sessions table created successfully")
        
        print()
        print("3. Verifying table creation:")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_sessions'")
        exists = cursor.fetchone()
        
        if exists:
            print("   ✅ Table verification successful")
            
            # Show table schema
            cursor.execute("PRAGMA table_info(audit_sessions)")
            columns = cursor.fetchall()
            print("   Table columns:")
            for col in columns:
                print(f"     - {col[1]} ({col[2]})")
        else:
            print("   ❌ Table verification failed")
            return False
        
        print()
        print("4. Checking if we need to migrate existing session data:")
        
        # Check if there are any sessions in payroll_sessions that should be in audit_sessions
        cursor.execute("SELECT COUNT(*) FROM payroll_sessions")
        payroll_session_count = cursor.fetchone()[0]
        
        if payroll_session_count > 0:
            print(f"   Found {payroll_session_count} sessions in payroll_sessions")
            print("   Note: These are different types of sessions and should not be migrated")
        else:
            print("   No existing sessions to consider")
        
        conn.close()
        
        print()
        print("✅ AUDIT_SESSIONS TABLE FIX COMPLETED!")
        print()
        print("The report generation should now work because:")
        print("- audit_sessions table exists")
        print("- Python code can create and query sessions")
        print("- Report generation can find the latest session")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_missing_audit_sessions_table()
