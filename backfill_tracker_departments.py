#!/usr/bin/env python3
"""
Phase 4: Data Migration and Backfill
Backfill existing tracker_results with department data from employees table
"""

import sys
import os
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def get_employee_department_fallback(employee_id):
    """Get fallback department based on employee ID pattern"""
    if employee_id.startswith('COP'):
        return 'POLICE SERVICE'
    elif employee_id.startswith('PW'):
        return 'PUBLIC WORKS'
    elif employee_id.startswith('MIN'):
        return 'MINISTRY'
    else:
        return 'DEPARTMENT NOT SPECIFIED'

def backfill_tracker_departments():
    """Backfill existing tracker_results with department information"""
    print("🔄 PHASE 4: BACKFILLING TRACKER RESULTS WITH DEPARTMENT DATA")
    print("=" * 70)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"📁 Using database: {db_path}")
        
        # 1. Check current status
        print("\n1. 📊 CURRENT STATUS:")
        cursor.execute("SELECT COUNT(*) FROM tracker_results")
        total_count = cursor.fetchone()[0]
        print(f"   Total tracker_results: {total_count}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NOT NULL")
        dept_count = cursor.fetchone()[0]
        print(f"   Records with department: {dept_count}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NULL")
        null_dept_count = cursor.fetchone()[0]
        print(f"   Records needing department: {null_dept_count}")
        
        if null_dept_count == 0:
            print("✅ All tracker_results already have department information!")
            return True
        
        # 2. Get all tracker_results that need department info
        print(f"\n2. 🔍 PROCESSING {null_dept_count} RECORDS:")
        cursor.execute("""
            SELECT id, session_id, employee_id, employee_name, tracker_type
            FROM tracker_results 
            WHERE department IS NULL
            ORDER BY session_id, employee_id
        """)
        
        records_to_update = cursor.fetchall()
        print(f"   Retrieved {len(records_to_update)} records to process")
        
        # 3. Process each record
        updated_count = 0
        fallback_count = 0
        error_count = 0
        
        print("\n3. 🔄 UPDATING RECORDS:")
        for i, record in enumerate(records_to_update):
            try:
                record_id, session_id, employee_id, employee_name, tracker_type = record
                
                # Try to get department from employees table
                cursor.execute("""
                    SELECT department, section FROM employees 
                    WHERE employee_id = ? AND session_id = ?
                    ORDER BY created_at DESC LIMIT 1
                """, (employee_id, session_id))
                
                emp_result = cursor.fetchone()
                
                if emp_result and emp_result[0] and str(emp_result[0]).strip() not in ['None', '', 'UNKNOWN', 'Unknown']:
                    # Use department from employees table
                    department = str(emp_result[0]).strip()
                    section = str(emp_result[1]).strip() if emp_result[1] and str(emp_result[1]).strip() not in ['None', '', 'UNKNOWN', 'Unknown'] else None
                else:
                    # Use fallback based on employee ID pattern
                    department = get_employee_department_fallback(employee_id)
                    section = None
                    fallback_count += 1
                
                # Update the tracker_results record
                cursor.execute("""
                    UPDATE tracker_results 
                    SET department = ?, section = ?
                    WHERE id = ?
                """, (department, section, record_id))
                
                updated_count += 1
                
                # Show progress for first few and every 100 records
                if updated_count <= 5 or updated_count % 100 == 0:
                    print(f"   ✅ {updated_count:4d}: {employee_id} -> {department}")
                
            except Exception as e:
                error_count += 1
                if error_count <= 5:
                    print(f"   ❌ Error updating {employee_id}: {e}")
        
        # 4. Commit changes
        conn.commit()
        
        # 5. Verify results
        print(f"\n4. ✅ BACKFILL COMPLETED:")
        print(f"   Records updated: {updated_count}")
        print(f"   Used fallback department: {fallback_count}")
        print(f"   Errors encountered: {error_count}")
        
        # Final verification
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NOT NULL")
        final_dept_count = cursor.fetchone()[0]
        print(f"   Final records with department: {final_dept_count}")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NULL")
        final_null_count = cursor.fetchone()[0]
        print(f"   Final records without department: {final_null_count}")
        
        # Show sample of updated data
        print(f"\n5. 📋 SAMPLE OF UPDATED DATA:")
        cursor.execute("""
            SELECT employee_id, employee_name, tracker_type, department
            FROM tracker_results 
            WHERE department IS NOT NULL
            LIMIT 5
        """)
        
        samples = cursor.fetchall()
        for sample in samples:
            print(f"   {sample[0]} | {sample[1]} | {sample[2]} | {sample[3]}")
        
        conn.close()
        
        print(f"\n✅ PHASE 4 COMPLETED SUCCESSFULLY")
        print(f"   - {updated_count} tracker_results updated with department information")
        print(f"   - {fallback_count} records used fallback department logic")
        print(f"   - Ready for Phase 5: Testing and Verification")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during backfill: {e}")
        return False

if __name__ == "__main__":
    success = backfill_tracker_departments()
    sys.exit(0 if success else 1)
