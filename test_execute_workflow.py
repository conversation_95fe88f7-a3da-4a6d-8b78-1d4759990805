#!/usr/bin/env python3
"""
Test the execute-workflow command that's failing
"""

import sys
import os
import json
sys.path.append('.')

def test_execute_workflow():
    """Test the execute-workflow command directly"""
    
    print("=== TESTING EXECUTE-WORKFLOW COMMAND ===")
    print()
    
    try:
        from core.phased_process_manager import PhasedProcessManager
        
        # Initialize process manager
        manager = PhasedProcessManager()
        manager.debug_mode = True
        
        print("1. Testing with sample PDFs:")
        
        # Use the same PDFs that exist in the system
        current_pdf = "JULY FILE/JULY.pdf"
        previous_pdf = "JULY FILE/JUNE.pdf"
        
        if not os.path.exists(current_pdf):
            print(f"   ❌ Current PDF not found: {current_pdf}")
            return
        
        if not os.path.exists(previous_pdf):
            print(f"   ❌ Previous PDF not found: {previous_pdf}")
            return
        
        print(f"   ✅ PDFs found: {current_pdf}, {previous_pdf}")
        
        # Create options similar to what the UI sends
        options = {
            'current_month': 'JULY',
            'current_year': '2025',
            'previous_month': 'JUNE',
            'previous_year': '2025',
            'signature_designation': 'Payroll Auditor',
            'report_type': 'standard'
        }
        
        print(f"   Options: {json.dumps(options, indent=2)}")
        
        print()
        print("2. Testing execute_complete_workflow:")
        
        # Test the complete workflow
        try:
            result = manager.execute_complete_workflow(current_pdf, previous_pdf, options)
            
            print(f"   Result: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print("   ✅ Workflow completed successfully!")
            else:
                print(f"   ❌ Workflow failed: {result.get('error')}")
                
        except Exception as e:
            print(f"   ❌ Workflow exception: {e}")
            import traceback
            traceback.print_exc()
        
        print()
        print("3. Testing individual extraction phase:")
        
        # Test just the extraction phase
        try:
            # Create a session first
            session_id = manager.create_session(current_pdf, previous_pdf, options)
            print(f"   ✅ Session created: {session_id}")
            
            # Test extraction phase
            extraction_result = manager._phase_extraction(options)
            
            if extraction_result:
                print("   ✅ Extraction phase completed successfully!")
            else:
                print("   ❌ Extraction phase returned False")
                
        except Exception as e:
            print(f"   ❌ Extraction phase exception: {e}")
            import traceback
            traceback.print_exc()
        
        print()
        print("✅ EXECUTE-WORKFLOW TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_execute_workflow()
