#!/usr/bin/env python3
"""
ENHANCED DICTIONARY AUTO-LEARNING SYSTEM
Real-time discovery and approval system for new payslip items
Integrates with Perfect Section-Aware Extractor for intelligent learning
"""

import os
import sys
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

# Add core directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

try:
    from core.python_database_manager import PythonDatabaseManager
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

@dataclass
class DiscoveredItem:
    """Represents a newly discovered payslip item"""
    session_id: str
    item_label: str
    section: str
    suggested_standard_name: str
    confidence_score: float
    occurrence_count: int = 1
    status: str = 'pending'
    first_seen_in: str = ''
    loan_classification: Optional[str] = None
    loan_type: Optional[str] = None
    column_type: Optional[str] = None
    created_at: str = ''

class EnhancedDictionaryAutoLearning:
    """
    Enhanced Auto-Learning System for Dictionary Management
    
    Features:
    - Real-time item discovery
    - Intelligent section classification
    - Loan type classification
    - Database persistence
    - Approval workflow
    """

    def __init__(self, dictionary_manager=None, debug=False):
        self.debug = debug
        self.dictionary_manager = dictionary_manager
        
        # Auto-learning session
        self.auto_learning_session = {
            'active': False,
            'session_id': None,
            'session_name': None,
            'start_time': None,
            'items_discovered': [],
            'items_pending': []
        }
        
        # Database connection
        self.database = None
        if DATABASE_AVAILABLE:
            try:
                self.database = PythonDatabaseManager()
                if self.debug:
                    print("✅ Auto-Learning database connected")
            except Exception as e:
                if self.debug:
                    print(f"⚠️ Database connection failed: {e}")
        
        # Discovered items cache
        self.discovered_items = {}
        self.pending_items = []
        
        if self.debug:
            print("🧠 ENHANCED DICTIONARY AUTO-LEARNING INITIALIZED")

    def start_auto_learning_session(self, session_name: str) -> str:
        """Start a new auto-learning session"""
        session_id = str(uuid.uuid4())
        
        self.auto_learning_session = {
            'active': True,
            'session_id': session_id,
            'session_name': session_name,
            'start_time': datetime.now().isoformat(),
            'items_discovered': [],
            'items_pending': []
        }
        
        # Save session to database
        if self.database:
            try:
                self.database.execute_query(
                    """INSERT INTO auto_learning_sessions 
                       (session_id, session_name, is_active, started_at)
                       VALUES (?, ?, 1, ?)""",
                    (session_id, session_name, self.auto_learning_session['start_time'])
                )
            except Exception as e:
                if self.debug:
                    print(f"⚠️ Failed to save session to database: {e}")
        
        if self.debug:
            print(f"🚀 AUTO-LEARNING SESSION STARTED: {session_name} ({session_id})")
        
        return session_id

    def discover_item(self, section: str, label: str, value: str, confidence: float = 1.0, 
                     source: str = "Perfect Section-Aware Extractor", 
                     loan_classification: Optional[str] = None,
                     loan_type: Optional[str] = None,
                     column_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Discover and classify a new payslip item
        
        Args:
            section: Section where item was found
            label: Original label text
            value: Extracted value
            confidence: Extraction confidence
            source: Source of extraction
            loan_classification: IN-HOUSE, EXTERNAL, or None
            loan_type: Type of loan if applicable
            column_type: LOAN, BALANCE B/F, CURRENT DEDUCTION, etc.
        
        Returns:
            Discovery result with classification
        """
        if not self.auto_learning_session['active']:
            return {'status': 'session_inactive'}
        
        # Generate unique key for item
        item_key = f"{section}.{label}".upper()
        
        # Check if already discovered
        if item_key in self.discovered_items:
            self.discovered_items[item_key]['occurrence_count'] += 1
            return {
                'status': 'already_discovered',
                'item_key': item_key,
                'occurrence_count': self.discovered_items[item_key]['occurrence_count']
            }
        
        # Create discovered item
        discovered_item = DiscoveredItem(
            session_id=self.auto_learning_session['session_id'],
            item_label=label,
            section=section,
            suggested_standard_name=self._suggest_standard_name(label),
            confidence_score=confidence,
            first_seen_in=source,
            loan_classification=loan_classification,
            loan_type=loan_type,
            column_type=column_type,
            created_at=datetime.now().isoformat()
        )
        
        # Store in cache
        self.discovered_items[item_key] = asdict(discovered_item)
        self.auto_learning_session['items_discovered'].append(asdict(discovered_item))
        
        # Add to pending approval if not in dictionary
        if not self._is_in_dictionary(section, label):
            self.pending_items.append(asdict(discovered_item))
            self.auto_learning_session['items_pending'].append(asdict(discovered_item))
            
            # Save to database
            if self.database:
                try:
                    self.database.execute_query(
                        """INSERT INTO pending_items 
                           (session_id, item_label, suggested_section, suggested_standard_name,
                            confidence_score, occurrence_count, first_seen_in, status)
                           VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')""",
                        (discovered_item.session_id, discovered_item.item_label, 
                         discovered_item.section, discovered_item.suggested_standard_name,
                         discovered_item.confidence_score, discovered_item.occurrence_count,
                         discovered_item.first_seen_in)
                    )
                except Exception as e:
                    if self.debug:
                        print(f"⚠️ Failed to save pending item: {e}")
        
        if self.debug:
            classification_info = ""
            if loan_classification:
                classification_info = f" [{loan_classification}]"
            print(f"ITEM DISCOVERED: {section}.{label} = {value} (confidence: {confidence}){classification_info}")
        
        return {
            'status': 'discovered',
            'item_key': item_key,
            'section': section,
            'label': label,
            'value': value,
            'confidence': confidence,
            'loan_classification': loan_classification,
            'suggested_standard_name': discovered_item.suggested_standard_name
        }

    def _suggest_standard_name(self, label: str) -> str:
        """Suggest a standardized name for the label"""
        # Clean and normalize label
        standard_name = label.upper().strip()
        
        # Remove common prefixes/suffixes
        standard_name = standard_name.replace(':', '').replace('.', '')
        
        # Replace spaces with underscores
        standard_name = standard_name.replace(' ', '_')
        
        # Handle common variations
        replacements = {
            'EMPLOYEE_NO': 'EMPLOYEE_NO',
            'EMPLOYEE_NUMBER': 'EMPLOYEE_NO',
            'EMP_NO': 'EMPLOYEE_NO',
            'EMPLOYEE_NAME': 'EMPLOYEE_NAME',
            'EMP_NAME': 'EMPLOYEE_NAME',
            'BASIC_SALARY': 'BASIC_SALARY',
            'GROSS_SALARY': 'GROSS_SALARY',
            'NET_PAY': 'NET_PAY',
            'TOTAL_DEDUCTIONS': 'TOTAL_DEDUCTIONS'
        }
        
        return replacements.get(standard_name, standard_name)

    def _is_in_dictionary(self, section: str, label: str) -> bool:
        """Check if item already exists in dictionary"""
        if not self.dictionary_manager:
            return False
        
        try:
            # Check if dictionary manager has this item
            dictionary = self.dictionary_manager.get_dictionary()
            section_key = section.lower().replace(' ', '_')
            
            if section_key in dictionary:
                section_items = dictionary[section_key].get('items', {})
                standard_name = self._suggest_standard_name(label)
                return standard_name in section_items
        except Exception as e:
            if self.debug:
                print(f"⚠️ Dictionary check failed: {e}")
        
        return False

    def get_pending_items(self) -> List[Dict[str, Any]]:
        """Get all pending items for approval"""
        if self.database:
            try:
                results = self.database.fetch_all(
                    "SELECT * FROM pending_items WHERE status = 'pending' ORDER BY created_at DESC"
                )
                return [dict(row) for row in results] if results else []
            except Exception as e:
                if self.debug:
                    print(f"⚠️ Failed to fetch pending items: {e}")
        
        return self.pending_items

    def approve_item(self, item_id: int) -> bool:
        """Approve a pending item and add to dictionary"""
        if self.database:
            try:
                self.database.execute_query(
                    "UPDATE pending_items SET status = 'approved', processed_at = ? WHERE id = ?",
                    (datetime.now().isoformat(), item_id)
                )
                return True
            except Exception as e:
                if self.debug:
                    print(f"⚠️ Failed to approve item: {e}")
        
        return False

    def reject_item(self, item_id: int, reason: str = '') -> bool:
        """Reject a pending item"""
        if self.database:
            try:
                self.database.execute_query(
                    "UPDATE pending_items SET status = 'rejected', rejection_reason = ?, processed_at = ? WHERE id = ?",
                    (reason, datetime.now().isoformat(), item_id)
                )
                return True
            except Exception as e:
                if self.debug:
                    print(f"⚠️ Failed to reject item: {e}")
        
        return False

    def get_session_stats(self) -> Dict[str, Any]:
        """Get current session statistics"""
        return {
            'session_active': self.auto_learning_session['active'],
            'session_id': self.auto_learning_session['session_id'],
            'session_name': self.auto_learning_session['session_name'],
            'items_discovered': len(self.auto_learning_session['items_discovered']),
            'items_pending': len(self.auto_learning_session['items_pending']),
            'start_time': self.auto_learning_session['start_time']
        }

def main():
    """Command line interface for auto-learning system"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced Dictionary Auto-Learning')
    parser.add_argument('command', help='Command: get-pending, get-stats')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    auto_learning = EnhancedDictionaryAutoLearning(debug=args.debug)
    
    if args.command == 'get-pending':
        pending = auto_learning.get_pending_items()
        print(json.dumps(pending, indent=2, default=str))
    elif args.command == 'get-stats':
        stats = auto_learning.get_session_stats()
        print(json.dumps(stats, indent=2, default=str))
    else:
        print('Available commands: get-pending, get-stats')

if __name__ == "__main__":
    main()
